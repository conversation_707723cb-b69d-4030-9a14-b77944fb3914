<?php
namespace App\Http\Controllers\API\admin;

use Carbon\Carbon;
use App\Models\Pack;
use App\Models\Agency;
use App\Models\Client;
use App\Models\Control;
use App\Models\Payment;
use App\Models\Personal;
use App\Models\Versement;
use App\Models\Cotisation;
use App\Models\Transaction;
use App\Models\Subscription;
use Illuminate\Http\Request;
use App\Models\PackPerformance;
use App\Models\ClientPerformance;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\API\helpers\HelperController;

class AccountingController extends HelperController
{
    public function getCashiers(Request $request)
    {
        $response = [];
        $status   = 200;
        try {
            $data     = [];
            $agencies = Agency::with(['wallet', 'subscriptions', 'currentCashiers'])
                ->withSum('cotisations', 'total_amount')
                ->withSum('versements', 'amount')
                ->withSum('subscriptions', 'price')
                ->get();

            foreach ($agencies as $key => $agency) {
                $data[] = [
                    'code_caisse'         => $agency->wallet->code,
                    'agency'              => $agency,
                    'cashier'             => $agency->currentCashiers->first(),
                    'total_versements'    => $agency->versements->sum('amount'),
                    'total_cotisations'   => $agency->cotisations->sum('total_amount'),
                    'total_subscriptions' => $agency->subscriptions->sum('price'),
                    'balance'             => $agency->wallet->balance,
                ];
            }
            $response = [
                'success' => true,
                'message' => "Liste des caisses",
                'result'  => $data,
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des caisses",
                'result'  => null,
                'errors'  => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function get_versements(Request $request)
    {
        $response = [];
        $status   = 200;
        try {
            $versements = Versement::with(['agency', 'collector', 'cashier'])->get();
            $response   = [
                'success' => true,
                'message' => "Liste des versements",
                'result'  => $versements,
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des versements",
                'result'  => null,
                'errors'  => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function getSubscriptions(Request $request)
    {
        $response = [];
        $status   = 200;
        try {
            $page          = $request->input('page', 1);
            $perPage       = $request->input('limit', 20);
            $statusFilter  = $request->input('status', '');
            $subscriptions = [];
            if ($statusFilter == '') {
                $subscriptions = Subscription::with(['client', 'pack', 'cotisation', 'collector', 'agency'])->paginate($perPage, ['*'], 'page', $page);
            } else {
                $subscriptions = Subscription::where('status', $statusFilter)->with(['client', 'pack', 'cotisation', 'collector', 'agency'])->paginate($perPage, ['*'], 'page', $page);
            }
            $response = [
                'success' => true,
                'message' => "Liste des abonnements",
                'result'  => $subscriptions,
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des abonnements",
                'result'  => null,
                'errors'  => $th->getMessage(),
            ];
            $status = 500; // Ensure this is an integer
        }
        return $this->apiResponse($response, (int) $status); // Cast $status to integer
    }

    public function getCotisations(Request $request)
    {
        $response = [];
        $status   = 200;
        try {
            $page     = $request->get('page', 1);
            $perPage  = $request->get('limit', 10);
            $agencyId = $request->get('agency_id', null);

            $query = Cotisation::orderBy('created_at', 'desc');
            if ($agencyId != null) {
                $query->where('agency_id', $agencyId);
            }
            $cotisations = $query->with([
                'client',
                'collector',
                'agency',
                'subscription' => function ($query) {
                    $query->with('pack');
                },
            ])->paginate($perPage, ['*'], 'page', $page);

            $response = [
                'success' => true,
                'message' => "Récupération des paiements ",
                'result'  => $cotisations,
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des abonnements",
                'result'  => null,
                'errors'  => $th->getMessage(),
            ];
            $status = 500; // Ensure this is an integer
        }
        return $this->apiResponse($response, (int) $status);
    }

    public function getPayments(Request $request)
    {
        $response = [];
        $status   = 200;
        try {
            $page     = $request->input('page', 1);
            $perPage  = 20;
            $payments = Payment::whereNotNull('cotisation_id')->orderBy('created_at', 'desc')->with([
                'cotisation',
                'client',
                'collector',
                'pack',
            ])->get();

            // Grouper les paiements par client, pack_id et date de paiement, et calculer la somme des paiements
            $groupedPayments = $payments->groupBy(function ($payment) {
                return $payment->client_id . '-' . $payment->cotisation->pack_id . '-' . Carbon::parse($payment->payment_date)->format('Y-m-d');
            })->map(function ($group) {
                $totalAmount     = $group->sum('amount');
                $client          = $group->first()->client;
                $pack            = $group->first()->pack;
                $collector       = $group->first()->collector;
                $cotisation      = $group->first()->cotisation;
                $paymentDate     = $group->first()->payment_date;
                $totalPaymentKey = $group->first()->client_id . '-' . $group->first()->pack_id . '-' . Carbon::parse($group->first()->payment_date)->format('Y-m-d');
                $keys            = $group->count();
                $tarif           = $group->first()->amount;

                return [
                    'client'       => $client,
                    'pack'         => $pack,
                    'payment_date' => $paymentDate,
                    'total_amount' => $totalAmount,
                    'collector'    => $collector,
                    'cotisation'   => $cotisation,
                    'payment_key'  => $totalPaymentKey,
                    'keys'         => $keys,
                    'tarif'        => $tarif,
                ];
            })->values(); // Utiliser values() pour réindexer la collection

            $response = [
                'success' => true,
                'message' => "Récupération des paiements ",
                'result'  => $groupedPayments,
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des paiements",
                'result'  => null,
                'errors'  => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function get_transactions(Request $request)
    {
        $response = [];
        $status   = 200;
        try {
            $transactions = Transaction::with(['user', 'agency', 'wallet'])->get();
            $response     = [
                'success' => true,
                'message' => "Liste des transactions",
                'result'  => $transactions,
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la recuperation des transactions",
                'result'  => null,
                'errors'  => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function get_controls(Request $request)
    {
        $response = [];
        $status   = 200;
        try {
            $controls = Control::orderBy('created_at', 'desc')->with([
                'supervisor',
                'collector',
                'subscription',
                'client',
            ])->get();
            $response = [
                'success' => true,
                'message' => "Liste des contrôles",
                'result'  => $controls,
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la recuperation des contrôles",
                'result'  => null,
                'errors'  => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function topCollectors()
    {
        $response = [];
        $status   = 200;
        try {
            $collectors = Personal::select([
                'personals.id',
                'personals.nom as last_name',
                'personals.prenoms as first_name',
                'personals.phone',
                'personals.email',
                DB::raw('COUNT(subscriptions.id) as total_subscriptions'),
                DB::raw('SUM(subscriptions.carnet_price) as total_revenue'),
                DB::raw('ROUND(AVG(subscriptions.carnet_price), 2) as average_sale_amount'),
                DB::raw('MAX(subscriptions.created_at) as last_subscription_date'),
            ])
                ->join('subscriptions', 'personals.id', '=', 'subscriptions.collector_id')
                ->whereIn('subscriptions.status', ['finished', 'delivered'])
                ->where('personals.role_id', 6)
                ->groupBy('personals.id', 'personals.nom', 'personals.prenoms', 'personals.phone', 'personals.email')
                ->orderByDesc('total_subscriptions')
                ->orderByDesc('total_revenue')
                ->limit(10)
                ->get();

            $response = [
                'success' => true,
                'result'  => $collectors,
                'message' => 'Top 10 collectors retrieved successfully',
            ];

        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'message' => "Echec de la recuperation des collecteurs",
                'result'  => null,
                'errors'  => $e->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function topClientsByRevenue()
    {
        try {
            $topClients = ClientPerformance::orderByDesc('total_revenue')->limit(10)->get();

            return $this->apiResponse([
                'success' => true,
                'result'  => $topClients,
                'message' => 'Top 10 clients récupérés depuis la table matérialisée',
            ]);
        } catch (\Exception $e) {
            return $this->apiResponse([
                'success' => false,
                'message' => 'Erreur lors de la récupération des clients',
                'error'   => $e->getMessage(),
            ], 500);
        }
    }

    public function getPopularPacks(Request $request)
    {
        $response = [];
        $status   = 200;
        try {
            $popularPacks = PackPerformance::orderByDesc('performance_score')
                ->orderByDesc('successful_subscriptions')
                ->limit(10)
                ->get();

            $response = [
                'success' => true,
                'result'  => $popularPacks,
                'message' => 'Top 10 popular packs retrieved successfully',
            ];
        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'message' => "Échec de la récupération des packs",
                'result'  => null,
                'errors'  => $e->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }
}
