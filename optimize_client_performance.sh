#!/bin/bash

echo "🚀 Optimisation des performances de RefreshClientPerformance"
echo "============================================================"

echo ""
echo "1️⃣ Exécution de la migration pour ajouter les index..."
php artisan migrate --path=database/migrations/2025_07_09_100000_add_performance_indexes.php

if [ $? -eq 0 ]; then
    echo "✅ Migration des index terminée avec succès"
else
    echo "❌ Erreur lors de la migration des index"
    exit 1
fi

echo ""
echo "2️⃣ Test de la commande optimisée..."
echo "⏱️ Début du test : $(date)"

# Exécuter la commande avec timeout de sécurité
timeout 600 php artisan refresh:client

if [ $? -eq 0 ]; then
    echo "✅ Commande terminée avec succès"
    echo "⏱️ Fin du test : $(date)"
else
    echo "❌ Erreur ou timeout lors de l'exécution de la commande"
    echo "⏱️ Fin du test : $(date)"
fi

echo ""
echo "3️⃣ Vérification des résultats..."
php artisan tinker --execute="
echo 'Nombre total de clients dans client_performances : ' . App\Models\ClientPerformance::count() . PHP_EOL;
echo 'Dernière mise à jour : ' . App\Models\ClientPerformance::max('last_updated') . PHP_EOL;
echo 'Top 5 clients par score d\'engagement :' . PHP_EOL;
App\Models\ClientPerformance::orderBy('engagement_score', 'desc')->limit(5)->get(['nom', 'prenoms', 'engagement_score', 'total_revenue'])->each(function(\$client) {
    echo '- ' . \$client->nom . ' ' . \$client->prenoms . ' : ' . \$client->engagement_score . '% (CA: ' . \$client->total_revenue . ')' . PHP_EOL;
});
"

echo ""
echo "🎉 Optimisation terminée !"
