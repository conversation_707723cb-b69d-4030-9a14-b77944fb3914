<?php

namespace App\Http\Controllers\API\auth;

use App\Http\Controllers\API\helpers\HelperController;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Services\FirebaseService;

class AuthController extends HelperController
{
    protected $firebaseService;

    public function __construct(FirebaseService $firebaseService) {
        $this->firebaseService = $firebaseService;
    }

    /**
     * login function
     *
     * @param Request $request
     * @return JsonResponse $response
     */
    public function login(Request $request) {
        //return $request->email;
        $response = [];
        try {
            $validate = Validator::make($request->all(),[
                'username'=>'required',
                'password'=>'required'
            ]);
            if ($validate->fails()) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>$validate->getMessageBag()
                ];
            } else {
                # code...
                $app_name = env('CLIENT_APP_NAME') ?? env('APP_NAME') ?? 'ProCollect';
                $user = User::where('email', $request->username)
                    ->orWhere('username',$request->username)
                    ->orWhere('phone',$request->username)
                    ->first();
                // return $user;
                if ($user != null) {
                    if ($user->status !== 1) {
                        # code...
                        $response = [
                            'success' => false,
                            'message' => "Utilisateur désactivé",
                            'except' => "User is disabled",
                            'result' => [
                                'user'=>$user,
                                'status'=>$user->status,
                            ],
                        ];
                    } else {
                        # code...
                        $check_pwd = Hash::check($request->password, $user->password);
                        if ($check_pwd) {
                            # check passowrd is correct
                            if ($user->role_id == 1 || $user->role_id == 2) {
                                # logged admin
                                $token = $user->createToken(time())->plainTextToken;
                                $expired_at = now()->addDay()->format('y-m-d H:i');
                                $user->is_online = 1;
                                $user->device_token = $request->device_token;
                                $user->save();
                                $response = [
                                    'success' => true,
                                    'message' => "Utilisateur authentifié avec succès",
                                    'result' => [
                                        'token'=>$token,
                                        'user'=>$user,
                                        'expired_at'=>$expired_at,
                                    ]
                                ];
                                // send push notification
                                $title = "Connexion réussie";
                                $body = "Bonjour, {$user->username}, vous êtes connecté avec succès à {$app_name}";
                                if($request->device_token !== null){
                                    $this->firebaseService->sendNotification($request->device_token, $title, $body);
                                }
                            } else {
                                # logged user
                                if ($user->is_online == 0 ) {
                                    # code...
                                    $token = $user->createToken(time())->plainTextToken;
                                    $expired_at = now()->addDay()->format('y-m-d H:i');
                                    $user->is_online = 1;
                                    $user->device_token = $request->device_token;
                                    $user->save();
                                    $response = [
                                        'success' => true,
                                        'message' => "Utilisateur authentifié avec succès",
                                        'result' => [
                                            'token'=>$token,
                                            'user'=>$user,
                                            'expired_at'=>$expired_at,
                                        ]
                                    ];
                                    // send push notification
                                    $title = "Connexion réussie";
                                    $body = "Bonjour, {$user->username}, vous êtes connecté avec succès à {$app_name}";
                                    if($request->device_token !== null){
                                        $this->firebaseService->sendNotification($request->device_token, $title, $body);
                                    }
                                }else{
                                    $response = [
                                        'success' => false,
                                        'message' => "Utilisateur est déjà connecté",
                                        'except' => "User is already connected",
                                        'result' => [
                                            'user'=>$user,
                                            'is_online'=>$user->is_online,
                                            'status'=>$user->status,
                                        ],
                                    ];
                                }
                            }

                        } else {
                            # incorrect password
                            $response = [
                                'success' => false,
                                'message' => "Nom d'utilisateur ou mot de passe incorrect",
                                'except' => 'Mot de passe incorrect',
                                'result' => null,
                            ];
                        }
                    }
                } else {
                    $response = [
                        'success' => false,
                        'message' => 'Email ou mot de passe incorrect',
                        'result' => null
                    ];
                }
            }

        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => 'Email ou mot de passe incorrect',
                'except' => $th->getMessage(),
            ];
        }
        return $this->apiResponse($response);
    }

    /**
     * logout function
     * @description: logout
     * @param Request $request
     * @return void
     */
    public function logout(Request $request){
        $response = [];
        try {
            $user = $request->user();
            $secret_code  = $request->secret_code;
            $can_continue = true;
            if (in_array($user->role_id,[3,4,5,6,7])) {
                if (Crypt::decryptString($user->secret_code) != $secret_code) {
                    $can_continue = false;
                    $response = [
                        'success' => false,
                        'message' => "Code secret incorrect",
                        'result' => null,
                    ];
                }
            }
            if ($can_continue) {
                $user->is_online = false;
                $is_logout = $request->user()->currentAccessToken()->delete();
                $up = ($is_logout) ? $user->save() : false;
                // return $request->user();
                $response = [
                    'success' => true,
                    'message' => "Vous avez été déconnecté avec succès",
                    'result' =>$is_logout
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "User does not disconnected",
                'exception' => $th->getMessage(),
                'result' =>null
            ];
        }
        return $this->apiResponse($response);
    }

    /**
     * auth_with_secretCode function
     * @description: authentification avec le code secret
     * @param Request $request
     * @return JsonResponse $response
     */
    public function auth_with_secretCode(Request $request){
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(),[
                'secret_code'=>'required',
                'user_id'=>'required'
            ]);
            if ($validate->fails()) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>$validate->errors()
                ];
            } else {
                $user = User::where('id',$request->user_id)->first();
                if ($user == null) {
                    $response = [
                        'success'=>false,
                        'message'=>"Code secret incorrect",
                        'result'=>null,
                        'errors'=>null
                    ];
                } else {
                    $secret_code = $request->secret_code; //Crypt::decrypt($request->secret_code);
                    if ($secret_code !== Crypt::decryptString($user->secret_code)) {
                        $response = [
                            'success'=>false,
                            'message'=>"Code secret incorrect",
                            'result'=>null,
                            'errors'=>null
                        ];
                    } else {
                        $old_user = $this->remove_old_user_auth($user->id);
                        if($old_user){
                            $token = $user->createToken(time())->plainTextToken;
                            $expired_at = now()->addDay()->format('y-m-d H:i');
                            $user->is_online = 1;
                            $user->save();
                            $response = [
                                'success'=>true,
                                'message'=>"Utilisateur authentifié avec succès",
                                'result'=>[
                                    'token'=>$token,
                                    'user'=>$user,
                                    'expired_at'=>$expired_at,
                                    'old_user'=>$old_user,
                                ]
                            ];
                        }else{
                            $response = [
                                'success'=>false,
                                'message'=>"Echec de connexion à votre compte",
                                'result'=>null,
                                'except'=>$old_user
                            ];
                        }
                    }
                }
            }
        } catch (\Throwable $th) {
            // throw $th;
            $response = [
                'success'=>false,
                'message'=>"Echec,une erreur s'est produite",
                'result'=>null,
                'except'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * remove_old_user_auth function
     *  @description: supprimer les anciens tokens d'authentification
     * @param integer $user_id
     * @return bool $is_trash
     */
    public function remove_old_user_auth(int $user_id){
        $is_trash = false;
        $res = DB::table('personal_access_tokens')->where('tokenable_id',$user_id)->delete();
        if ($res !== null) {
            $is_trash = true;
        }
        return $is_trash;
    }

    public function getEncryptionSalt(Request $request){
        return response()->json([
            'success'=>true,
            'message'=>"Salt",
            'result'=>config('app.key')
        ]);
    }


}
