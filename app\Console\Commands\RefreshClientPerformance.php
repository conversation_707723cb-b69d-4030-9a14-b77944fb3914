<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ClientPerformance;
use Illuminate\Support\Facades\DB;

class RefreshClientPerformance extends Command
{
    protected $signature   = 'refresh:client-v1';
    protected $description = 'Recalcule les statistiques des clients actifs et met à jour la table client_performances';

    /**
     * Execute the console command.
     */

    public function handle()
    {
        $this->info("🔍 Recherche des clients impactés...");

        // Augmenter temporairement les timeouts MySQL
        $this->setMySQLTimeouts();

        try {
            $isEmpty = ClientPerformance::count() === 0;

            if ($isEmpty) {
                $this->info("🆕 Aucune donnée trouvée. Mise à jour complète...");
                $this->processAllClients();
            } else {
                $this->processRecentlyUpdatedClients();
            }
        } finally {
            // Restaurer les timeouts par défaut
            $this->restoreMySQLTimeouts();
        }
    }

    /**
     * Configure les timeouts MySQL pour les requêtes longues
     */
    private function setMySQLTimeouts()
    {
        try {
            DB::statement('SET SESSION max_execution_time = 300000'); // 5 minutes
            DB::statement('SET SESSION wait_timeout = 300');
            DB::statement('SET SESSION interactive_timeout = 300');
            $this->info("⚙️ Timeouts MySQL configurés pour les requêtes longues");
        } catch (\Exception $e) {
            $this->warn("⚠️ Impossible de configurer les timeouts MySQL : " . $e->getMessage());
        }
    }

    /**
     * Restaure les timeouts MySQL par défaut
     */
    private function restoreMySQLTimeouts()
    {
        try {
            DB::statement('SET SESSION max_execution_time = DEFAULT');
            DB::statement('SET SESSION wait_timeout = DEFAULT');
            DB::statement('SET SESSION interactive_timeout = DEFAULT');
            $this->info("⚙️ Timeouts MySQL restaurés");
        } catch (\Exception $e) {
            $this->warn("⚠️ Impossible de restaurer les timeouts MySQL : " . $e->getMessage());
        }
    }

    /**
     * Traite tous les clients (initialisation complète)
     */
    private function processAllClients()
    {
        ClientPerformance::truncate();

        $batchSize = 1000;
        $offset = 0;
        $totalProcessed = 0;

        do {
            $clientIds = DB::table('clients')
                ->select('id')
                ->offset($offset)
                ->limit($batchSize)
                ->pluck('id')
                ->toArray();

            if (empty($clientIds)) {
                break;
            }

            $processed = $this->processClientBatch($clientIds);
            $totalProcessed += $processed;
            $offset += $batchSize;

            $this->info("📊 Traité : {$totalProcessed} clients...");
        } while (count($clientIds) === $batchSize);

        $this->info("✅ Initialisation terminée : {$totalProcessed} clients ajoutés !");
    }

    /**
     * Traite uniquement les clients récemment modifiés
     */
    private function processRecentlyUpdatedClients()
    {
        // Récupère les IDs des clients modifiés ces 24 dernières heures
        $recentClientIds = DB::select("
            SELECT DISTINCT c.id
            FROM clients c
            WHERE c.updated_at > NOW() - INTERVAL 24 HOUR

            UNION

            SELECT DISTINCT s.client_id as id
            FROM subscriptions s
            WHERE s.updated_at > NOW() - INTERVAL 24 HOUR

            UNION

            SELECT DISTINCT co.client_id as id
            FROM cotisations co
            WHERE co.updated_at > NOW() - INTERVAL 24 HOUR
        ");

        if (empty($recentClientIds)) {
            $this->info("✅ Aucun client à mettre à jour.");
            return;
        }

        $idsToUpdate = array_map(fn($row) => $row->id, $recentClientIds);
        $this->info("♻️ " . count($idsToUpdate) . " clients trouvés. Mise à jour en cours...");

        // Suppression des anciennes données pour ces clients
        ClientPerformance::whereIn('id', $idsToUpdate)->delete();

        // Traitement par batch
        $batches = array_chunk($idsToUpdate, 500);
        $totalProcessed = 0;

        foreach ($batches as $batch) {
            $processed = $this->processClientBatch($batch);
            $totalProcessed += $processed;
            $this->info("📊 Traité : {$totalProcessed}/" . count($idsToUpdate) . " clients...");
        }

        $this->info("✅ {$totalProcessed} clients mis à jour !");
    }

    /**
     * Traite un batch de clients avec requêtes optimisées
     */
    private function processClientBatch(array $clientIds): int
    {
        if (empty($clientIds)) {
            return 0;
        }

        // Récupération des données clients de base
        $clients = DB::table('clients')
            ->select('id', 'nom', 'prenoms', 'phone', 'email')
            ->whereIn('id', $clientIds)
            ->get()
            ->keyBy('id');

        // Récupération des données de subscriptions optimisée
        $subscriptionData = DB::table('subscriptions')
            ->select(
                'client_id',
                DB::raw('COUNT(CASE WHEN status IN ("finished", "delivered") THEN 1 END) as finished_count'),
                DB::raw('COALESCE(SUM(CASE WHEN status IN ("finished", "delivered") THEN carnet_price ELSE 0 END), 0) as revenue')
            )
            ->whereIn('client_id', $clientIds)
            ->groupBy('client_id')
            ->get()
            ->keyBy('client_id');

        // Récupération des données de cotisations optimisée
        $cotisationData = DB::table('cotisations')
            ->select(
                'client_id',
                DB::raw('COUNT(CASE WHEN status = "finished" THEN 1 END) as finished_count'),
                DB::raw('COALESCE(SUM(CASE WHEN status = "finished" THEN total_amount ELSE 0 END), 0) as revenue')
            )
            ->whereIn('client_id', $clientIds)
            ->groupBy('client_id')
            ->get()
            ->keyBy('client_id');

        $results = [];
        foreach ($clients as $clientId => $client) {
            $subscription = $subscriptionData->get($clientId);
            $cotisation = $cotisationData->get($clientId);

            $subscriptionRevenue = $subscription ? (float) $subscription->revenue : 0;
            $cotisationRevenue = $cotisation ? (float) $cotisation->revenue : 0;
            $totalRevenue = $subscriptionRevenue + $cotisationRevenue;

            $finishedSubscriptions = $subscription ? (int) $subscription->finished_count : 0;
            $finishedCotisations = $cotisation ? (int) $cotisation->finished_count : 0;

            // Calcul du score d'engagement
            $engagementScore = round(
                ($finishedSubscriptions * 0.4) +
                ($subscriptionRevenue / 10000 * 0.4) +
                ($cotisationRevenue / 10000 * 0.2),
                2
            );

            $results[] = (object) [
                'id' => $clientId,
                'nom' => $client->nom,
                'prenoms' => $client->prenoms,
                'phone' => $client->phone,
                'email' => $client->email,
                'finished_subscriptions_count' => $finishedSubscriptions,
                'subscription_revenue' => $subscriptionRevenue,
                'finished_cotisations_count' => $finishedCotisations,
                'cotisation_revenue' => $cotisationRevenue,
                'total_revenue' => $totalRevenue,
                'engagement_score' => $engagementScore,
            ];
        }

        if (empty($results)) {
            return 0;
        }

        // Calcul du score max pour normalisation
        $scores = array_map(fn($r) => $r->engagement_score, $results);
        $maxScore = max($scores);

        // Insertion en batch
        $insertData = [];
        foreach ($results as $row) {
            $normalizedScore = $maxScore > 0 ? round(($row->engagement_score / $maxScore) * 100, 2) : 0;
            $normalizedScore = min(100.00, $normalizedScore);

            $insertData[] = [
                'id' => $row->id,
                'nom' => $row->nom,
                'prenoms' => $row->prenoms,
                'phone' => $row->phone,
                'email' => $row->email,
                'finished_subscriptions_count' => $row->finished_subscriptions_count,
                'subscription_revenue' => $row->subscription_revenue,
                'finished_cotisations_count' => $row->finished_cotisations_count,
                'cotisation_revenue' => $row->cotisation_revenue,
                'total_revenue' => $row->total_revenue,
                'engagement_score' => $normalizedScore,
                'last_updated' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Insertion en batch pour de meilleures performances
        DB::table('client_performances')->insert($insertData);

        return count($results);
    }
}
