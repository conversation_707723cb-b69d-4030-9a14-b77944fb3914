<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // agencies table
        Schema::table('agencies', function (Blueprint $table) {
            $table->foreign('parent_id')->references('id')->on('agencies')->onDelete('set null');
            $table->foreign('city_id')->references('id')->on('cities')->onDelete('cascade');
            $table->foreign('quarter_id')->references('id')->on('quarters')->onDelete('set null');
            $table->foreign('responsable_id')->references('id')->on('personals')->onDelete('set null');
            $table->index('city_id');
            $table->index('quarter_id');
            $table->index('responsable_id');
            $table->index('status');
            $table->index('name');
        });

        // agency_personals table
        Schema::table('agency_personals', function (Blueprint $table) {
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('cascade');
            $table->foreign('personal_id')->references('id')->on('personals')->onDelete('cascade');
            $table->foreign('role_id')->references('id')->on('roles')->onDelete('cascade');
            $table->index('agency_id');
            $table->index('personal_id');
            $table->index('role_id');
            $table->index('is_current');
        });

        // app_configurations table
        Schema::table('app_configurations', function (Blueprint $table) {
            $table->index('app_type');
        });

        // categories table
        Schema::table('categories', function (Blueprint $table) {
            $table->index('name');
        });

        // cities table
        Schema::table('cities', function (Blueprint $table) {
            $table->foreign('country_id')->references('id')->on('countries')->onDelete('cascade');
            $table->index('country_id');
            $table->index('name');
        });

        // client_collectors table
        Schema::table('client_collectors', function (Blueprint $table) {
            $table->foreign('client_id')->references('id')->on('clients')->onDelete('cascade');
            $table->foreign('collector_id')->references('id')->on('personals')->onDelete('cascade');
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('cascade');
            $table->index('client_id');
            $table->index('collector_id');
            $table->index('agency_id');
            $table->index('is_principal');
        });

        // client_performances table
        Schema::table('client_performances', function (Blueprint $table) {
            $table->index('nom');
            $table->index('phone');
            $table->index('email');
            $table->index('last_updated');
        });

        // clients table
        Schema::table('clients', function (Blueprint $table) {
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('set null');
            $table->foreign('city_id')->references('id')->on('cities')->onDelete('set null');
            $table->foreign('quarter_id')->references('id')->on('quarters')->onDelete('set null');
            $table->foreign('collector_id')->references('id')->on('personals')->onDelete('set null');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->index('agency_id');
            $table->index('city_id');
            $table->index('quarter_id');
            $table->index('collector_id');
            $table->index('user_id');
            $table->index('status');
            $table->index('nom');
        });

        // controls table
        Schema::table('controls', function (Blueprint $table) {
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('cascade');
            $table->foreign('supervisor_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('collector_id')->references('id')->on('personals')->onDelete('cascade');
            $table->foreign('client_id')->references('id')->on('clients')->onDelete('cascade');
            $table->foreign('subscription_id')->references('id')->on('subscriptions')->onDelete('cascade');
            $table->index('agency_id');
            $table->index('supervisor_id');
            $table->index('collector_id');
            $table->index('client_id');
            $table->index('subscription_id');
            $table->index('status');
            $table->index('date_control');
        });

        // cotisations table
        Schema::table('cotisations', function (Blueprint $table) {
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('cascade');
            $table->foreign('client_id')->references('id')->on('clients')->onDelete('cascade');
            $table->foreign('collector_id')->references('id')->on('personals')->onDelete('cascade');
            $table->foreign('subscription_id')->references('id')->on('subscriptions')->onDelete('cascade');
            $table->index('agency_id');
            $table->index('client_id');
            $table->index('collector_id');
            $table->index('subscription_id');
            $table->index('status');
            $table->index('date_cotise');
        });

        // countries table
        Schema::table('countries', function (Blueprint $table) {
            $table->index('name');
            $table->index('code');
        });

        // deliveries table (already has foreign keys for client_id and pack_id)
        Schema::table('deliveries', function (Blueprint $table) {
            $table->foreign('delivery_men')->references('id')->on('personals')->onDelete('set null');
            $table->index('delivery_men');
            $table->index('delivery_date');
        });

        // failed_jobs table
        Schema::table('failed_jobs', function (Blueprint $table) {
            $table->index('failed_at');
        });

        // pack_performances table
        Schema::table('pack_performances', function (Blueprint $table) {
            $table->index('pack_name');
            $table->index('last_updated');
        });

        // pack_products table
        Schema::table('pack_products', function (Blueprint $table) {
            $table->foreign('pack_id')->references('id')->on('packs')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
            $table->index('pack_id');
            $table->index('product_id');
            $table->index('status');
        });

        // packs table
        Schema::table('packs', function (Blueprint $table) {
            $table->index('name');
            $table->index('status');
            $table->index('category');
        });

        // payments table
        Schema::table('payments', function (Blueprint $table) {
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('cascade');
            $table->foreign('collector_id')->references('id')->on('personals')->onDelete('cascade');
            $table->foreign('subscription_id')->references('id')->on('subscriptions')->onDelete('set null');
            $table->foreign('cotisation_id')->references('id')->on('cotisations')->onDelete('set null');
            $table->foreign('pack_id')->references('id')->on('packs')->onDelete('set null');
            $table->foreign('client_id')->references('id')->on('clients')->onDelete('set null');
            $table->foreign('versement_id')->references('id')->on('versements')->onDelete('set null');
            $table->index('agency_id');
            $table->index('collector_id');
            $table->index('subscription_id');
            $table->index('cotisation_id');
            $table->index('pack_id');
            $table->index('client_id');
            $table->index('versement_id');
            $table->index('status');
            $table->index('payment_date');
            $table->index('payment_mode');
        });


        // personal_access_tokens table
        Schema::table('personal_access_tokens', function (Blueprint $table) {
            $table->index('tokenable_type');
            $table->index('tokenable_id');
            $table->index('created_at');
        });

        // personals table
        Schema::table('personals', function (Blueprint $table) {
            $table->foreign('city_id')->references('id')->on('cities')->onDelete('set null');
            $table->foreign('quarter_id')->references('id')->on('quarters')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('role_id')->references('id')->on('roles')->onDelete('set null');
            $table->index('city_id');
            $table->index('quarter_id');
            $table->index('user_id');
            $table->index('role_id');
            $table->index('status');
            $table->index('nom');
            $table->index('email');
            $table->index('phone');
        });

        // products table
        Schema::table('products', function (Blueprint $table) {
            $table->foreign('category_id')->references('id')->on('categories')->onDelete('set null');
            $table->index('category_id');
            $table->index('name');
            $table->index('status');
            $table->index('code');
        });

        // quarters table
        Schema::table('quarters', function (Blueprint $table) {
            $table->foreign('city_id')->references('id')->on('cities')->onDelete('cascade');
            $table->index('city_id');
            $table->index('name');
        });

        // roles table
        Schema::table('roles', function (Blueprint $table) {
            $table->index('name');
        });

        // stocks table (already has product_id foreign key)
        Schema::table('stocks', function (Blueprint $table) {
            $table->index('reference');
            $table->index('status');
            $table->index('date_stock');
        });

        // subscriptions table
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('set null');
            $table->foreign('pack_id')->references('id')->on('packs')->onDelete('cascade');
            $table->foreign('client_id')->references('id')->on('clients')->onDelete('cascade');
            $table->foreign('collector_id')->references('id')->on('personals')->onDelete('set null');
            $table->index('agency_id');
            $table->index('pack_id');
            $table->index('client_id');
            $table->index('collector_id');
            $table->index('status');
            $table->index('code');
        });

        // transactions table
        Schema::table('transactions', function (Blueprint $table) {
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('set null');
            $table->foreign('wallet_id')->references('id')->on('wallets')->onDelete('cascade');
            $table->index('user_id');
            $table->index('agency_id');
            $table->index('wallet_id');
            $table->index('status');
            $table->index('date_transact');
        });

        // users table
        Schema::table('users', function (Blueprint $table) {
            $table->foreign('role_id')->references('id')->on('roles')->onDelete('cascade');
            $table->index('role_id');
            $table->index('username');
            $table->index('status');
        });

        // versements table
        Schema::table('versements', function (Blueprint $table) {
            $table->foreign('collector_id')->references('id')->on('personals')->onDelete('set null');
            $table->foreign('cashier_id')->references('id')->on('personals')->onDelete('set null');
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('set null');
            $table->index('collector_id');
            $table->index('cashier_id');
            $table->index('agency_id');
            $table->index('status');
            $table->index('payment_date');
            $table->index('trx_ref');
        });

        // wallets table
        Schema::table('wallets', function (Blueprint $table) {
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('set null');
            $table->index('user_id');
            $table->index('agency_id');
            $table->index('status');
            $table->index('code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // wallets table
        Schema::table('wallets', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
            $table->dropForeign(['agency_id']);
            $table->dropIndex(['user_id']);
            $table->dropIndex(['agency_id']);
            $table->dropIndex(['status']);
            $table->dropIndex(['code']);
        });

        // versements table
        Schema::table('versements', function (Blueprint $table) {
            $table->dropForeign(['collector_id']);
            $table->dropForeign(['cashier_id']);
            $table->dropForeign(['agency_id']);
            $table->dropIndex(['collector_id']);
            $table->dropIndex(['cashier_id']);
            $table->dropIndex(['agency_id']);
            $table->dropIndex(['status']);
            $table->dropIndex(['payment_date']);
            $table->dropIndex(['trx_ref']);
        });

        // users table
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['role_id']);
            $table->dropIndex(['role_id']);
            $table->dropIndex(['username']);
            $table->dropIndex(['status']);
        });

        // transactions table
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
            $table->dropForeign(['agency_id']);
            $table->dropForeign(['wallet_id']);
            $table->dropIndex(['user_id']);
            $table->dropIndex(['agency_id']);
            $table->dropIndex(['wallet_id']);
            $table->dropIndex(['status']);
            $table->dropIndex(['date_transact']);
        });

        // subscriptions table
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropForeign(['agency_id']);
            $table->dropForeign(['pack_id']);
            $table->dropForeign(['client_id']);
            $table->dropForeign(['collector_id']);
            $table->dropIndex(['agency_id']);
            $table->dropIndex(['pack_id']);
            $table->dropIndex(['client_id']);
            $table->dropIndex(['collector_id']);
            $table->dropIndex(['status']);
            $table->dropIndex(['code']);
        });

        // stocks table
        Schema::table('stocks', function (Blueprint $table) {
            $table->dropIndex(['reference']);
            $table->dropIndex(['status']);
            $table->dropIndex(['date_stock']);
        });

        // roles table
        Schema::table('roles', function (Blueprint $table) {
            $table->dropIndex(['name']);
        });

        // quarters table
        Schema::table('quarters', function (Blueprint $table) {
            $table->dropForeign(['city_id']);
            $table->dropIndex(['city_id']);
            $table->dropIndex(['name']);
        });

        // products table
        Schema::table('products', function (Blueprint $table) {
            $table->dropForeign(['category_id']);
            $table->dropIndex(['category_id']);
            $table->dropIndex(['name']);
            $table->dropIndex(['status']);
            $table->dropIndex(['code']);
        });

        // personals table
        Schema::table('personals', function (Blueprint $table) {
            $table->dropForeign(['city_id']);
            $table->dropForeign(['quarter_id']);
            $table->dropForeign(['user_id']);
            $table->dropForeign(['role_id']);
            $table->dropIndex(['city_id']);
            $table->dropIndex(['quarter_id']);
            $table->dropIndex(['user_id']);
            $table->dropIndex(['role_id']);
            $table->dropIndex(['status']);
            $table->dropIndex(['nom']);
            $table->dropIndex(['email']);
            $table->dropIndex(['phone']);
        });

        // personal_access_tokens table
        Schema::table('personal_access_tokens', function (Blueprint $table) {
            $table->dropIndex(['tokenable_type']);
            $table->dropIndex(['tokenable_id']);
            $table->dropIndex(['created_at']);
        });

        // permissions table
        Schema::table('permissions', function (Blueprint $table) {
            $table->dropIndex(['name']);
        });

        // payments table
        Schema::table('payments', function (Blueprint $table) {
            $table->dropForeign(['agency_id']);
            $table->dropForeign(['collector_id']);
            $table->dropForeign(['subscription_id']);
            $table->dropForeign(['cotisation_id']);
            $table->dropForeign(['pack_id']);
            $table->dropForeign(['client_id']);
            $table->dropForeign(['versement_id']);
            $table->dropIndex(['agency_id']);
            $table->dropIndex(['collector_id']);
            $table->dropIndex(['subscription_id']);
            $table->dropIndex(['cotisation_id']);
            $table->dropIndex(['pack_id']);
            $table->dropIndex(['client_id']);
            $table->dropIndex(['versement_id']);
            $table->dropIndex(['status']);
            $table->dropIndex(['payment_date']);
            $table->dropIndex(['payment_mode']);
        });

        // password_reset_tokens table
        Schema::table('password_reset_tokens', function (Blueprint $table) {
            $table->dropIndex(['email']);
            $table->dropIndex(['created_at']);
        });

        // packs table
        Schema::table('packs', function (Blueprint $table) {
            $table->dropIndex(['name']);
            $table->dropIndex(['status']);
            $table->dropIndex(['category']);
        });

        // pack_products table
        Schema::table('pack_products', function (Blueprint $table) {
            $table->dropForeign(['pack_id']);
            $table->dropForeign(['product_id']);
            $table->dropIndex(['pack_id']);
            $table->dropIndex(['product_id']);
            $table->dropIndex(['status']);
        });

        // pack_performances table
        Schema::table('pack_performances', function (Blueprint $table) {
            $table->dropIndex(['pack_name']);
            $table->dropIndex(['last_updated']);
        });

        // migrations table
        Schema::table('migrations', function (Blueprint $table) {
            $table->dropIndex(['migration']);
        });

        // failed_jobs table
        Schema::table('failed_jobs', function (Blueprint $table) {
            $table->dropIndex(['failed_at']);
        });

        // deliveries table
        Schema::table('deliveries', function (Blueprint $table) {
            $table->dropForeign(['delivery_men']);
            $table->dropIndex(['delivery_men']);
            $table->dropIndex(['delivery_date']);
        });

        // countries table
        Schema::table('countries', function (Blueprint $table) {
            $table->dropIndex(['name']);
            $table->dropIndex(['code']);
        });

        // cotisations table
        Schema::table('cotisations', function (Blueprint $table) {
            $table->dropForeign(['agency_id']);
            $table->dropForeign(['client_id']);
            $table->dropForeign(['collector_id']);
            $table->dropForeign(['subscription_id']);
            $table->dropIndex(['agency_id']);
            $table->dropIndex(['client_id']);
            $table->dropIndex(['collector_id']);
            $table->dropIndex(['subscription_id']);
            $table->dropIndex(['status']);
            $table->dropIndex(['date_cotise']);
        });

        // controls table
        Schema::table('controls', function (Blueprint $table) {
            $table->dropForeign(['agency_id']);
            $table->dropForeign(['supervisor_id']);
            $table->dropForeign(['collector_id']);
            $table->dropForeign(['client_id']);
            $table->dropForeign(['subscription_id']);
            $table->dropIndex(['agency_id']);
            $table->dropIndex(['supervisor_id']);
            $table->dropIndex(['collector_id']);
            $table->dropIndex(['client_id']);
            $table->dropIndex(['subscription_id']);
            $table->dropIndex(['status']);
            $table->dropIndex(['date_control']);
        });

        // clients table
        Schema::table('clients', function (Blueprint $table) {
            $table->dropForeign(['agency_id']);
            $table->dropForeign(['city_id']);
            $table->dropForeign(['quarter_id']);
            $table->dropForeign(['collector_id']);
            $table->dropForeign(['user_id']);
            $table->dropIndex(['agency_id']);
            $table->dropIndex(['city_id']);
            $table->dropIndex(['quarter_id']);
            $table->dropIndex(['collector_id']);
            $table->dropIndex(['user_id']);
            $table->dropIndex(['status']);
            $table->dropIndex(['nom']);
            $table->dropIndex(['phone']);
            $table->dropIndex(['email']);
        });

        // client_performances table
        Schema::table('client_performances', function (Blueprint $table) {
            $table->dropIndex(['nom']);
            $table->dropIndex(['phone']);
            $table->dropIndex(['email']);
            $table->dropIndex(['last_updated']);
        });

        // client_collectors table
        Schema::table('client_collectors', function (Blueprint $table) {
            $table->dropForeign(['client_id']);
            $table->dropForeign(['collector_id']);
            $table->dropForeign(['agency_id']);
            $table->dropIndex(['client_id']);
            $table->dropIndex(['collector_id']);
            $table->dropIndex(['agency_id']);
            $table->dropIndex(['is_principal']);
        });

        // cities table
        Schema::table('cities', function (Blueprint $table) {
            $table->dropForeign(['country_id']);
            $table->dropIndex(['country_id']);
            $table->dropIndex(['name']);
        });

        // categories table
        Schema::table('categories', function (Blueprint $table) {
            $table->dropIndex(['name']);
            $table->dropIndex(['slug']);
        });

        // app_configurations table
        Schema::table('app_configurations', function (Blueprint $table) {
            $table->dropIndex(['app_type']);
        });

        // agencies table
        Schema::table('agencies', function (Blueprint $table) {
            $table->dropForeign(['parent_id']);
            $table->dropForeign(['city_id']);
            $table->dropForeign(['quarter_id']);
            $table->dropForeign(['responsable_id']);
            $table->dropIndex(['city_id']);
            $table->dropIndex(['quarter_id']);
            $table->dropIndex(['responsable_id']);
            $table->dropIndex(['status']);
            $table->dropIndex(['name']);
        });
    }
};
