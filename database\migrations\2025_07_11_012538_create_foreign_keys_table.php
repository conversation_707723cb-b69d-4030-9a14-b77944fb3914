<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        try {
            // wallets table
            Schema::table('wallets', function (Blueprint $table) {
                $table->dropForeign(['user_id']);
                $table->dropForeign(['agency_id']);
                $table->dropIndex(['user_id']);
                $table->dropIndex(['agency_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['code']);
            });

            // versements table
            Schema::table('versements', function (Blueprint $table) {
                $table->dropForeign(['collector_id']);
                $table->dropForeign(['cashier_id']);
                $table->dropForeign(['agency_id']);
                $table->dropIndex(['collector_id']);
                $table->dropIndex(['cashier_id']);
                $table->dropIndex(['agency_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['payment_date']);
                $table->dropIndex(['trx_ref']);
                // Supprimer les index composites
                $table->dropIndex('idx_vers_coll_status');
                $table->dropIndex('idx_vers_cash_status');
                $table->dropIndex('idx_vers_agency_status');
                $table->dropIndex('idx_vers_agency_date');
                $table->dropIndex('idx_vers_coll_date');
                $table->dropIndex('idx_vers_status_created');
            });

            // users table
            Schema::table('users', function (Blueprint $table) {
                $table->dropForeign(['role_id']);
                $table->dropIndex(['role_id']);
                $table->dropIndex(['username']);
                $table->dropIndex(['status']);
            });

            // transactions table
            Schema::table('transactions', function (Blueprint $table) {
                $table->dropForeign(['user_id']);
                $table->dropForeign(['agency_id']);
                $table->dropForeign(['wallet_id']);
                $table->dropIndex(['user_id']);
                $table->dropIndex(['agency_id']);
                $table->dropIndex(['wallet_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['date_transact']);
                // Supprimer les index composites
                $table->dropIndex('idx_trans_user_status');
                $table->dropIndex('idx_trans_agency_date');
                $table->dropIndex('idx_trans_wallet_status');
                $table->dropIndex('idx_trans_status_date');
            });

            // subscriptions table
            Schema::table('subscriptions', function (Blueprint $table) {
                $table->dropForeign(['agency_id']);
                $table->dropForeign(['pack_id']);
                $table->dropForeign(['client_id']);
                $table->dropForeign(['collector_id']);
                $table->dropIndex(['agency_id']);
                $table->dropIndex(['pack_id']);
                $table->dropIndex(['client_id']);
                $table->dropIndex(['collector_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['code']);
            });

            // stocks table
            Schema::table('stocks', function (Blueprint $table) {
                $table->dropIndex(['reference']);
                $table->dropIndex(['status']);
                $table->dropIndex(['date_stock']);
            });

            // roles table
            Schema::table('roles', function (Blueprint $table) {
                $table->dropIndex(['name']);
            });

            // quarters table
            Schema::table('quarters', function (Blueprint $table) {
                $table->dropForeign(['city_id']);
                $table->dropIndex(['city_id']);
                $table->dropIndex(['name']);
            });

            // products table
            Schema::table('products', function (Blueprint $table) {
                $table->dropForeign(['category_id']);
                $table->dropIndex(['category_id']);
                $table->dropIndex(['name']);
                $table->dropIndex(['status']);
                $table->dropIndex(['code']);
            });

            // personals table
            Schema::table('personals', function (Blueprint $table) {
                $table->dropForeign(['city_id']);
                $table->dropForeign(['quarter_id']);
                $table->dropForeign(['user_id']);
                $table->dropForeign(['role_id']);
                $table->dropIndex(['city_id']);
                $table->dropIndex(['quarter_id']);
                $table->dropIndex(['user_id']);
                $table->dropIndex(['role_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['nom']);
                $table->dropIndex(['email']);
                $table->dropIndex(['phone']);
            });

            // personal_access_tokens table
            Schema::table('personal_access_tokens', function (Blueprint $table) {
                $table->dropIndex(['tokenable_type']);
                $table->dropIndex(['tokenable_id']);
                $table->dropIndex(['created_at']);
            });

            // permissions table
            Schema::table('permissions', function (Blueprint $table) {
                $table->dropIndex(['name']);
            });

            // payments table
            Schema::table('payments', function (Blueprint $table) {
                $table->dropForeign(['agency_id']);
                $table->dropForeign(['collector_id']);
                $table->dropForeign(['subscription_id']);
                $table->dropForeign(['cotisation_id']);
                $table->dropForeign(['pack_id']);
                $table->dropForeign(['client_id']);
                $table->dropForeign(['versement_id']);
                $table->dropIndex(['agency_id']);
                $table->dropIndex(['collector_id']);
                $table->dropIndex(['subscription_id']);
                $table->dropIndex(['cotisation_id']);
                $table->dropIndex(['pack_id']);
                $table->dropIndex(['client_id']);
                $table->dropIndex(['versement_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['payment_date']);
                $table->dropIndex(['payment_mode']);
                // Supprimer les index composites
                $table->dropIndex('idx_pay_coll_date');
                $table->dropIndex('idx_pay_agency_date');
                $table->dropIndex('idx_pay_cotis_date');
                $table->dropIndex('idx_pay_sub_cotis');
                $table->dropIndex('idx_pay_type_status');
                $table->dropIndex('idx_pay_coll_status');
                $table->dropIndex('idx_pay_agency_status');
            });

            // password_reset_tokens table
            Schema::table('password_reset_tokens', function (Blueprint $table) {
                $table->dropIndex(['email']);
                $table->dropIndex(['created_at']);
            });

            // packs table
            Schema::table('packs', function (Blueprint $table) {
                $table->dropIndex(['name']);
                $table->dropIndex(['status']);
                $table->dropIndex(['category']);
            });

            // pack_products table
            Schema::table('pack_products', function (Blueprint $table) {
                $table->dropForeign(['pack_id']);
                $table->dropForeign(['product_id']);
                $table->dropIndex(['pack_id']);
                $table->dropIndex(['product_id']);
                $table->dropIndex(['status']);
            });

            // pack_performances table
            Schema::table('pack_performances', function (Blueprint $table) {
                $table->dropIndex(['pack_name']);
                $table->dropIndex(['last_updated']);
            });

            // migrations table
            Schema::table('migrations', function (Blueprint $table) {
                $table->dropIndex(['migration']);
            });

            // failed_jobs table
            Schema::table('failed_jobs', function (Blueprint $table) {
                $table->dropIndex(['failed_at']);
            });

            // deliveries table
            Schema::table('deliveries', function (Blueprint $table) {
                $table->dropForeign(['delivery_men']);
                $table->dropIndex(['delivery_men']);
                $table->dropIndex(['delivery_date']);
            });

            // countries table
            Schema::table('countries', function (Blueprint $table) {
                $table->dropIndex(['name']);
                $table->dropIndex(['code']);
            });

            // cotisations table
            Schema::table('cotisations', function (Blueprint $table) {
                $table->dropForeign(['agency_id']);
                $table->dropForeign(['client_id']);
                $table->dropForeign(['collector_id']);
                $table->dropForeign(['subscription_id']);
                $table->dropIndex(['agency_id']);
                $table->dropIndex(['client_id']);
                $table->dropIndex(['collector_id']);
                $table->dropIndex(['subscription_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['date_cotise']);
            });

            // controls table
            Schema::table('controls', function (Blueprint $table) {
                $table->dropForeign(['agency_id']);
                $table->dropForeign(['supervisor_id']);
                $table->dropForeign(['collector_id']);
                $table->dropForeign(['client_id']);
                $table->dropForeign(['subscription_id']);
                $table->dropIndex(['agency_id']);
                $table->dropIndex(['supervisor_id']);
                $table->dropIndex(['collector_id']);
                $table->dropIndex(['client_id']);
                $table->dropIndex(['subscription_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['date_control']);
                // Supprimer les index composites
                $table->dropIndex('idx_ctrl_agency_date');
                $table->dropIndex('idx_ctrl_coll_status');
                $table->dropIndex('idx_ctrl_super_date');
                $table->dropIndex('idx_ctrl_client_status');
            });

            // clients table
            Schema::table('clients', function (Blueprint $table) {
                $table->dropForeign(['agency_id']);
                $table->dropForeign(['city_id']);
                $table->dropForeign(['quarter_id']);
                $table->dropForeign(['collector_id']);
                $table->dropForeign(['user_id']);
                $table->dropIndex(['agency_id']);
                $table->dropIndex(['city_id']);
                $table->dropIndex(['quarter_id']);
                $table->dropIndex(['collector_id']);
                $table->dropIndex(['user_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['nom']);
                $table->dropIndex(['phone']);
                $table->dropIndex(['email']);
            });

            // client_performances table
            Schema::table('client_performances', function (Blueprint $table) {
                $table->dropIndex(['nom']);
                $table->dropIndex(['phone']);
                $table->dropIndex(['email']);
                $table->dropIndex(['last_updated']);
            });

            // client_collectors table
            Schema::table('client_collectors', function (Blueprint $table) {
                $table->dropForeign(['client_id']);
                $table->dropForeign(['collector_id']);
                $table->dropForeign(['agency_id']);
                $table->dropIndex(['client_id']);
                $table->dropIndex(['collector_id']);
                $table->dropIndex(['agency_id']);
                $table->dropIndex(['is_principal']);
            });

            // cities table
            Schema::table('cities', function (Blueprint $table) {
                $table->dropForeign(['country_id']);
                $table->dropIndex(['country_id']);
                $table->dropIndex(['name']);
            });

            // categories table
            Schema::table('categories', function (Blueprint $table) {
                $table->dropIndex(['name']);
                $table->dropIndex(['slug']);
            });

            // app_configurations table
            Schema::table('app_configurations', function (Blueprint $table) {
                $table->dropIndex(['app_type']);
            });

            // agencies table
            Schema::table('agencies', function (Blueprint $table) {
                $table->dropForeign(['parent_id']);
                $table->dropForeign(['city_id']);
                $table->dropForeign(['quarter_id']);
                $table->dropForeign(['responsable_id']);
                $table->dropIndex(['city_id']);
                $table->dropIndex(['quarter_id']);
                $table->dropIndex(['responsable_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['name']);
                // Supprimer les index composites
                $table->dropIndex('idx_agency_city_status');
                $table->dropIndex('idx_agency_parent_status');
                $table->dropIndex('idx_agency_resp_status');
            });
        } finally {
            // Réactiver la vérification des clés étrangères
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        }
    }



    /**
     * Reverse the migrations.
     */
    public function up(): void
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        try {
            // wallets table
            Schema::table('wallets', function (Blueprint $table) {
                $table->dropForeign(['user_id']);
                $table->dropForeign(['agency_id']);
                $table->dropIndex(['user_id']);
                $table->dropIndex(['agency_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['code']);
            });

            // versements table
            Schema::table('versements', function (Blueprint $table) {
                $table->dropForeign(['collector_id']);
                $table->dropForeign(['cashier_id']);
                $table->dropForeign(['agency_id']);
                $table->dropIndex(['collector_id']);
                $table->dropIndex(['cashier_id']);
                $table->dropIndex(['agency_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['payment_date']);
                $table->dropIndex(['trx_ref']);
                // Supprimer les index composites
                $table->dropIndex('idx_vers_coll_status');
                $table->dropIndex('idx_vers_cash_status');
                $table->dropIndex('idx_vers_agency_status');
                $table->dropIndex('idx_vers_agency_date');
                $table->dropIndex('idx_vers_coll_date');
                $table->dropIndex('idx_vers_status_created');
            });

            // users table
            Schema::table('users', function (Blueprint $table) {
                $table->dropForeign(['role_id']);
                $table->dropIndex(['role_id']);
                $table->dropIndex(['username']);
                $table->dropIndex(['status']);
            });

            // transactions table
            Schema::table('transactions', function (Blueprint $table) {
                $table->dropForeign(['user_id']);
                $table->dropForeign(['agency_id']);
                $table->dropForeign(['wallet_id']);
                $table->dropIndex(['user_id']);
                $table->dropIndex(['agency_id']);
                $table->dropIndex(['wallet_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['date_transact']);
                // Supprimer les index composites
                $table->dropIndex('idx_trans_user_status');
                $table->dropIndex('idx_trans_agency_date');
                $table->dropIndex('idx_trans_wallet_status');
                $table->dropIndex('idx_trans_status_date');
            });

            // subscriptions table
            Schema::table('subscriptions', function (Blueprint $table) {
                $table->dropForeign(['agency_id']);
                $table->dropForeign(['pack_id']);
                $table->dropForeign(['client_id']);
                $table->dropForeign(['collector_id']);
                $table->dropIndex(['agency_id']);
                $table->dropIndex(['pack_id']);
                $table->dropIndex(['client_id']);
                $table->dropIndex(['collector_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['code']);
            });

            // stocks table
            Schema::table('stocks', function (Blueprint $table) {
                $table->dropIndex(['reference']);
                $table->dropIndex(['status']);
                $table->dropIndex(['date_stock']);
            });

            // roles table
            Schema::table('roles', function (Blueprint $table) {
                $table->dropIndex(['name']);
            });

            // quarters table
            Schema::table('quarters', function (Blueprint $table) {
                $table->dropForeign(['city_id']);
                $table->dropIndex(['city_id']);
                $table->dropIndex(['name']);
            });

            // products table
            Schema::table('products', function (Blueprint $table) {
                $table->dropForeign(['category_id']);
                $table->dropIndex(['category_id']);
                $table->dropIndex(['name']);
                $table->dropIndex(['status']);
                $table->dropIndex(['code']);
            });

            // personals table
            Schema::table('personals', function (Blueprint $table) {
                $table->dropForeign(['city_id']);
                $table->dropForeign(['quarter_id']);
                $table->dropForeign(['user_id']);
                $table->dropForeign(['role_id']);
                $table->dropIndex(['city_id']);
                $table->dropIndex(['quarter_id']);
                $table->dropIndex(['user_id']);
                $table->dropIndex(['role_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['nom']);
                $table->dropIndex(['email']);
                $table->dropIndex(['phone']);
            });

            // personal_access_tokens table
            Schema::table('personal_access_tokens', function (Blueprint $table) {
                $table->dropIndex(['tokenable_type']);
                $table->dropIndex(['tokenable_id']);
                $table->dropIndex(['created_at']);
            });

            // permissions table
            Schema::table('permissions', function (Blueprint $table) {
                $table->dropIndex(['name']);
            });

            // payments table
            Schema::table('payments', function (Blueprint $table) {
                $table->dropForeign(['agency_id']);
                $table->dropForeign(['collector_id']);
                $table->dropForeign(['subscription_id']);
                $table->dropForeign(['cotisation_id']);
                $table->dropForeign(['pack_id']);
                $table->dropForeign(['client_id']);
                $table->dropForeign(['versement_id']);
                $table->dropIndex(['agency_id']);
                $table->dropIndex(['collector_id']);
                $table->dropIndex(['subscription_id']);
                $table->dropIndex(['cotisation_id']);
                $table->dropIndex(['pack_id']);
                $table->dropIndex(['client_id']);
                $table->dropIndex(['versement_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['payment_date']);
                $table->dropIndex(['payment_mode']);
                // Supprimer les index composites
                $table->dropIndex('idx_pay_coll_date');
                $table->dropIndex('idx_pay_agency_date');
                $table->dropIndex('idx_pay_cotis_date');
                $table->dropIndex('idx_pay_sub_cotis');
                $table->dropIndex('idx_pay_type_status');
                $table->dropIndex('idx_pay_coll_status');
                $table->dropIndex('idx_pay_agency_status');
            });

            // password_reset_tokens table
            Schema::table('password_reset_tokens', function (Blueprint $table) {
                $table->dropIndex(['email']);
                $table->dropIndex(['created_at']);
            });

            // packs table
            Schema::table('packs', function (Blueprint $table) {
                $table->dropIndex(['name']);
                $table->dropIndex(['status']);
                $table->dropIndex(['category']);
            });

            // pack_products table
            Schema::table('pack_products', function (Blueprint $table) {
                $table->dropForeign(['pack_id']);
                $table->dropForeign(['product_id']);
                $table->dropIndex(['pack_id']);
                $table->dropIndex(['product_id']);
                $table->dropIndex(['status']);
            });

            // pack_performances table
            Schema::table('pack_performances', function (Blueprint $table) {
                $table->dropIndex(['pack_name']);
                $table->dropIndex(['last_updated']);
            });

            // migrations table
            Schema::table('migrations', function (Blueprint $table) {
                $table->dropIndex(['migration']);
            });

            // failed_jobs table
            Schema::table('failed_jobs', function (Blueprint $table) {
                $table->dropIndex(['failed_at']);
            });

            // deliveries table
            Schema::table('deliveries', function (Blueprint $table) {
                $table->dropForeign(['delivery_men']);
                $table->dropIndex(['delivery_men']);
                $table->dropIndex(['delivery_date']);
            });

            // countries table
            Schema::table('countries', function (Blueprint $table) {
                $table->dropIndex(['name']);
                $table->dropIndex(['code']);
            });

            // cotisations table
            Schema::table('cotisations', function (Blueprint $table) {
                $table->dropForeign(['agency_id']);
                $table->dropForeign(['client_id']);
                $table->dropForeign(['collector_id']);
                $table->dropForeign(['subscription_id']);
                $table->dropIndex(['agency_id']);
                $table->dropIndex(['client_id']);
                $table->dropIndex(['collector_id']);
                $table->dropIndex(['subscription_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['date_cotise']);
            });

            // controls table
            Schema::table('controls', function (Blueprint $table) {
                $table->dropForeign(['agency_id']);
                $table->dropForeign(['supervisor_id']);
                $table->dropForeign(['collector_id']);
                $table->dropForeign(['client_id']);
                $table->dropForeign(['subscription_id']);
                $table->dropIndex(['agency_id']);
                $table->dropIndex(['supervisor_id']);
                $table->dropIndex(['collector_id']);
                $table->dropIndex(['client_id']);
                $table->dropIndex(['subscription_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['date_control']);
                // Supprimer les index composites
                $table->dropIndex('idx_ctrl_agency_date');
                $table->dropIndex('idx_ctrl_coll_status');
                $table->dropIndex('idx_ctrl_super_date');
                $table->dropIndex('idx_ctrl_client_status');
            });

            // clients table
            Schema::table('clients', function (Blueprint $table) {
                $table->dropForeign(['agency_id']);
                $table->dropForeign(['city_id']);
                $table->dropForeign(['quarter_id']);
                $table->dropForeign(['collector_id']);
                $table->dropForeign(['user_id']);
                $table->dropIndex(['agency_id']);
                $table->dropIndex(['city_id']);
                $table->dropIndex(['quarter_id']);
                $table->dropIndex(['collector_id']);
                $table->dropIndex(['user_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['nom']);
                $table->dropIndex(['phone']);
                $table->dropIndex(['email']);
            });

            // client_performances table
            Schema::table('client_performances', function (Blueprint $table) {
                $table->dropIndex(['nom']);
                $table->dropIndex(['phone']);
                $table->dropIndex(['email']);
                $table->dropIndex(['last_updated']);
            });

            // client_collectors table
            Schema::table('client_collectors', function (Blueprint $table) {
                $table->dropForeign(['client_id']);
                $table->dropForeign(['collector_id']);
                $table->dropForeign(['agency_id']);
                $table->dropIndex(['client_id']);
                $table->dropIndex(['collector_id']);
                $table->dropIndex(['agency_id']);
                $table->dropIndex(['is_principal']);
            });

            // cities table
            Schema::table('cities', function (Blueprint $table) {
                $table->dropForeign(['country_id']);
                $table->dropIndex(['country_id']);
                $table->dropIndex(['name']);
            });

            // categories table
            Schema::table('categories', function (Blueprint $table) {
                $table->dropIndex(['name']);
                $table->dropIndex(['slug']);
            });

            // app_configurations table
            Schema::table('app_configurations', function (Blueprint $table) {
                $table->dropIndex(['app_type']);
            });

            // agencies table
            Schema::table('agencies', function (Blueprint $table) {
                $table->dropForeign(['parent_id']);
                $table->dropForeign(['city_id']);
                $table->dropForeign(['quarter_id']);
                $table->dropForeign(['responsable_id']);
                $table->dropIndex(['city_id']);
                $table->dropIndex(['quarter_id']);
                $table->dropIndex(['responsable_id']);
                $table->dropIndex(['status']);
                $table->dropIndex(['name']);
                // Supprimer les index composites
                $table->dropIndex('idx_agency_city_status');
                $table->dropIndex('idx_agency_parent_status');
                $table->dropIndex('idx_agency_resp_status');
            });
        } finally {
            // Réactiver la vérification des clés étrangères
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        }
    }
};
