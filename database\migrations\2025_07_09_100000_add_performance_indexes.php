<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            // Index pour optimiser les JOINs sur client_id
            $table->index('client_id', 'idx_subscriptions_client_id');
            
            // Index pour optimiser les conditions WHERE sur status
            $table->index('status', 'idx_subscriptions_status');
            
            // Index composé pour optimiser les requêtes avec client_id et status
            $table->index(['client_id', 'status'], 'idx_subscriptions_client_status');
            
            // Index pour optimiser la détection des changements récents
            $table->index('updated_at', 'idx_subscriptions_updated_at');
            
            // Index composé pour optimiser les requêtes de changements récents par client
            $table->index(['client_id', 'updated_at'], 'idx_subscriptions_client_updated');
        });

        Schema::table('cotisations', function (Blueprint $table) {
            // Index pour optimiser les JOINs sur client_id
            $table->index('client_id', 'idx_cotisations_client_id');
            
            // Index pour optimiser les conditions WHERE sur status
            $table->index('status', 'idx_cotisations_status');
            
            // Index composé pour optimiser les requêtes avec client_id et status
            $table->index(['client_id', 'status'], 'idx_cotisations_client_status');
            
            // Index pour optimiser la détection des changements récents
            $table->index('updated_at', 'idx_cotisations_updated_at');
            
            // Index composé pour optimiser les requêtes de changements récents par client
            $table->index(['client_id', 'updated_at'], 'idx_cotisations_client_updated');
        });

        Schema::table('clients', function (Blueprint $table) {
            // Index pour optimiser la détection des changements récents
            $table->index('updated_at', 'idx_clients_updated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropIndex('idx_subscriptions_client_id');
            $table->dropIndex('idx_subscriptions_status');
            $table->dropIndex('idx_subscriptions_client_status');
            $table->dropIndex('idx_subscriptions_updated_at');
            $table->dropIndex('idx_subscriptions_client_updated');
        });

        Schema::table('cotisations', function (Blueprint $table) {
            $table->dropIndex('idx_cotisations_client_id');
            $table->dropIndex('idx_cotisations_status');
            $table->dropIndex('idx_cotisations_client_status');
            $table->dropIndex('idx_cotisations_updated_at');
            $table->dropIndex('idx_cotisations_client_updated');
        });

        Schema::table('clients', function (Blueprint $table) {
            $table->dropIndex('idx_clients_updated_at');
        });
    }
};
