[2025-07-05 02:58:39] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-05 02:58:49] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-05 02:59:02] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-05 03:01:36] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-05 03:02:00] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-05 03:02:08] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-05 03:10:06] local.ERROR: syntax error, unexpected end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected end of file at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php:275)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\WorkSpace\\\\FR...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\WorkSpace\\\\FR...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\WorkSpace\\\\FR...')
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Providers\\RouteServiceProvider.php(36): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\WorkSpace\\\\FR...')
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 24)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-05 03:10:07] local.ERROR: syntax error, unexpected end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected end of file at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php:275)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\WorkSpace\\\\FR...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\WorkSpace\\\\FR...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\WorkSpace\\\\FR...')
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Providers\\RouteServiceProvider.php(36): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\WorkSpace\\\\FR...')
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 24)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-05 03:10:07] local.ERROR: syntax error, unexpected end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected end of file at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php:275)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\WorkSpace\\\\FR...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\WorkSpace\\\\FR...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\WorkSpace\\\\FR...')
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Providers\\RouteServiceProvider.php(36): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\WorkSpace\\\\FR...')
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 24)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-05 03:10:08] local.ERROR: syntax error, unexpected end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected end of file at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php:275)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\WorkSpace\\\\FR...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\WorkSpace\\\\FR...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\WorkSpace\\\\FR...')
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Providers\\RouteServiceProvider.php(36): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\WorkSpace\\\\FR...')
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 24)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-05 03:10:09] local.ERROR: syntax error, unexpected end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected end of file at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php:275)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\WorkSpace\\\\FR...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\WorkSpace\\\\FR...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\WorkSpace\\\\FR...')
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Providers\\RouteServiceProvider.php(36): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\WorkSpace\\\\FR...')
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 24)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-05 03:10:10] local.ERROR: syntax error, unexpected end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected end of file at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php:275)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\WorkSpace\\\\FR...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\WorkSpace\\\\FR...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\WorkSpace\\\\FR...')
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Providers\\RouteServiceProvider.php(36): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\WorkSpace\\\\FR...')
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 24)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-05 03:10:10] local.ERROR: syntax error, unexpected end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected end of file at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php:275)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\WorkSpace\\\\FR...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\WorkSpace\\\\FR...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\WorkSpace\\\\FR...')
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Providers\\RouteServiceProvider.php(36): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\WorkSpace\\\\FR...')
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 24)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-05 03:10:14] local.ERROR: syntax error, unexpected end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected end of file at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php:275)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\WorkSpace\\\\FR...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\WorkSpace\\\\FR...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\WorkSpace\\\\FR...')
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Providers\\RouteServiceProvider.php(36): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\WorkSpace\\\\FR...')
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 24)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-05 03:10:15] local.ERROR: syntax error, unexpected end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected end of file at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php:275)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\WorkSpace\\\\FR...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\WorkSpace\\\\FR...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\WorkSpace\\\\FR...')
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Providers\\RouteServiceProvider.php(36): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\WorkSpace\\\\FR...')
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 24)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-05 03:10:15] local.ERROR: syntax error, unexpected end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected end of file at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php:275)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\WorkSpace\\\\FR...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\WorkSpace\\\\FR...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\WorkSpace\\\\FR...')
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Providers\\RouteServiceProvider.php(36): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\WorkSpace\\\\FR...')
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 24)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-05 03:10:19] local.ERROR: syntax error, unexpected end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected end of file at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php:275)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\WorkSpace\\\\FR...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\WorkSpace\\\\FR...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\WorkSpace\\\\FR...')
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Providers\\RouteServiceProvider.php(36): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\WorkSpace\\\\FR...')
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 24)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-05 03:10:19] local.ERROR: syntax error, unexpected end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected end of file at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php:275)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\WorkSpace\\\\FR...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\WorkSpace\\\\FR...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\WorkSpace\\\\FR...')
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Providers\\RouteServiceProvider.php(36): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\WorkSpace\\\\FR...')
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 24)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-05 03:10:20] local.ERROR: syntax error, unexpected end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected end of file at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php:275)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\WorkSpace\\\\FR...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\WorkSpace\\\\FR...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\WorkSpace\\\\FR...')
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Providers\\RouteServiceProvider.php(36): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\WorkSpace\\\\FR...')
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 24)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-05 03:10:21] local.ERROR: syntax error, unexpected end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected end of file at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php:275)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\WorkSpace\\\\FR...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\WorkSpace\\\\FR...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\WorkSpace\\\\FR...')
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Providers\\RouteServiceProvider.php(36): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\WorkSpace\\\\FR...')
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 24)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-05 03:10:21] local.ERROR: syntax error, unexpected end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected end of file at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php:275)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\WorkSpace\\\\FR...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\WorkSpace\\\\FR...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\WorkSpace\\\\FR...')
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Providers\\RouteServiceProvider.php(36): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\WorkSpace\\\\FR...')
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 24)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-05 03:10:47] local.ERROR: syntax error, unexpected variable "$user" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected variable \"$user\" at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php:272)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\WorkSpace\\\\FR...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\WorkSpace\\\\FR...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\WorkSpace\\\\FR...')
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Providers\\RouteServiceProvider.php(36): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\WorkSpace\\\\FR...')
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 24)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-05 03:10:47] local.ERROR: syntax error, unexpected variable "$user" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected variable \"$user\" at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php:272)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\WorkSpace\\\\FR...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\WorkSpace\\\\FR...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\WorkSpace\\\\FR...')
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Providers\\RouteServiceProvider.php(36): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\WorkSpace\\\\FR...')
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 24)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-05 03:10:48] local.ERROR: syntax error, unexpected variable "$user" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected variable \"$user\" at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php:272)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\WorkSpace\\\\FR...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\WorkSpace\\\\FR...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\WorkSpace\\\\FR...')
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Providers\\RouteServiceProvider.php(36): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\WorkSpace\\\\FR...')
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1037): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1015): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 24)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): array_walk(Array, Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-05 03:13:25] local.ERROR: could not find driver (Connection: mysql, SQL: select * from `users` where `users`.`id` = 1 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: mysql, SQL: select * from `users` where `users`.`id` = 1 limit 1) at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(449): Illuminate\\Database\\Eloquent\\Builder->first(Array)
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->find(1)
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'find', Array)
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('find', Array)
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php(273): Illuminate\\Database\\Eloquent\\Model::__callStatic('find', Array)
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(208): Illuminate\\Routing\\Route->runCallable()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\WorkSpace\\\\FR...')
#51 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', 'root', Array)
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(449): Illuminate\\Database\\Eloquent\\Builder->first(Array)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->find(1)
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'find', Array)
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('find', Array)
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php(273): Illuminate\\Database\\Eloquent\\Model::__callStatic('find', Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(208): Illuminate\\Routing\\Route->runCallable()
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#33 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#34 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#35 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\WorkSpace\\\\FR...')
#61 {main}
"} 
[2025-07-05 03:25:00] local.ERROR: could not find driver (Connection: mysql, SQL: select * from `users` where `users`.`id` = 1 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: mysql, SQL: select * from `users` where `users`.`id` = 1 limit 1) at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(449): Illuminate\\Database\\Eloquent\\Builder->first(Array)
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->find(1)
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'find', Array)
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('find', Array)
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php(273): Illuminate\\Database\\Eloquent\\Model::__callStatic('find', Array)
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(208): Illuminate\\Routing\\Route->runCallable()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\WorkSpace\\\\FR...')
#51 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', 'root', Array)
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(449): Illuminate\\Database\\Eloquent\\Builder->first(Array)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->find(1)
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'find', Array)
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('find', Array)
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php(273): Illuminate\\Database\\Eloquent\\Model::__callStatic('find', Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(208): Illuminate\\Routing\\Route->runCallable()
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#33 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#34 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#35 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\WorkSpace\\\\FR...')
#61 {main}
"} 
[2025-07-05 03:25:05] local.ERROR: could not find driver (Connection: mysql, SQL: select * from `users` where `users`.`id` = 1 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: mysql, SQL: select * from `users` where `users`.`id` = 1 limit 1) at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(449): Illuminate\\Database\\Eloquent\\Builder->first(Array)
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->find(1)
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'find', Array)
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('find', Array)
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php(273): Illuminate\\Database\\Eloquent\\Model::__callStatic('find', Array)
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(208): Illuminate\\Routing\\Route->runCallable()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\WorkSpace\\\\FR...')
#51 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', 'root', Array)
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(449): Illuminate\\Database\\Eloquent\\Builder->first(Array)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->find(1)
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'find', Array)
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('find', Array)
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php(273): Illuminate\\Database\\Eloquent\\Model::__callStatic('find', Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(208): Illuminate\\Routing\\Route->runCallable()
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#33 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#34 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#35 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\WorkSpace\\\\FR...')
#61 {main}
"} 
[2025-07-05 03:25:08] local.ERROR: could not find driver (Connection: mysql, SQL: select * from `users` where `users`.`id` = 1 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: mysql, SQL: select * from `users` where `users`.`id` = 1 limit 1) at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(449): Illuminate\\Database\\Eloquent\\Builder->first(Array)
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->find(1)
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'find', Array)
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('find', Array)
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php(273): Illuminate\\Database\\Eloquent\\Model::__callStatic('find', Array)
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(208): Illuminate\\Routing\\Route->runCallable()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\WorkSpace\\\\FR...')
#51 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', 'root', Array)
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(449): Illuminate\\Database\\Eloquent\\Builder->first(Array)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->find(1)
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'find', Array)
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('find', Array)
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php(273): Illuminate\\Database\\Eloquent\\Model::__callStatic('find', Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(208): Illuminate\\Routing\\Route->runCallable()
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#33 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#34 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#35 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\WorkSpace\\\\FR...')
#61 {main}
"} 
[2025-07-05 03:25:15] local.ERROR: could not find driver (Connection: mysql, SQL: select * from `users` where `users`.`id` = 1 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: mysql, SQL: select * from `users` where `users`.`id` = 1 limit 1) at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(449): Illuminate\\Database\\Eloquent\\Builder->first(Array)
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->find(1)
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'find', Array)
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('find', Array)
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php(273): Illuminate\\Database\\Eloquent\\Model::__callStatic('find', Array)
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(208): Illuminate\\Routing\\Route->runCallable()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\WorkSpace\\\\FR...')
#51 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', 'root', Array)
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(449): Illuminate\\Database\\Eloquent\\Builder->first(Array)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->find(1)
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'find', Array)
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('find', Array)
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php(273): Illuminate\\Database\\Eloquent\\Model::__callStatic('find', Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(208): Illuminate\\Routing\\Route->runCallable()
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#33 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#34 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#35 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\WorkSpace\\\\FR...')
#61 {main}
"} 
[2025-07-05 03:29:13] local.ERROR: could not find driver (Connection: mysql, SQL: select * from `users` where `users`.`id` = 1 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: mysql, SQL: select * from `users` where `users`.`id` = 1 limit 1) at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(449): Illuminate\\Database\\Eloquent\\Builder->first(Array)
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->find(1)
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'find', Array)
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('find', Array)
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php(273): Illuminate\\Database\\Eloquent\\Model::__callStatic('find', Array)
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(208): Illuminate\\Routing\\Route->runCallable()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\WorkSpace\\\\FR...')
#51 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', 'root', Array)
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(449): Illuminate\\Database\\Eloquent\\Builder->first(Array)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->find(1)
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'find', Array)
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('find', Array)
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php(273): Illuminate\\Database\\Eloquent\\Model::__callStatic('find', Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(208): Illuminate\\Routing\\Route->runCallable()
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#33 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#34 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#35 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\WorkSpace\\\\FR...')
#61 {main}
"} 
[2025-07-05 08:30:11] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-05 08:30:49] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-05 08:31:04] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-05 08:52:19] local.ERROR: could not find driver (Connection: mysql, SQL: select * from `users` where `users`.`id` = 1 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: mysql, SQL: select * from `users` where `users`.`id` = 1 limit 1) at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(449): Illuminate\\Database\\Eloquent\\Builder->first(Array)
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->find(1)
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'find', Array)
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('find', Array)
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php(273): Illuminate\\Database\\Eloquent\\Model::__callStatic('find', Array)
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(208): Illuminate\\Routing\\Route->runCallable()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\WorkSpace\\\\FR...')
#51 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', 'root', Array)
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(449): Illuminate\\Database\\Eloquent\\Builder->first(Array)
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->find(1)
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'find', Array)
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('find', Array)
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\routes\\api.php(273): Illuminate\\Database\\Eloquent\\Model::__callStatic('find', Array)
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(208): Illuminate\\Routing\\Route->runCallable()
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#33 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#34 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#35 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\WorkSpace\\\\FR...')
#61 {main}
"} 
[2025-07-05 11:13:47] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-05 11:13:51] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-05 11:13:54] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-05 11:29:41] local.ERROR: Maximum execution time of 60 seconds exceeded {"userId":1,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:430)
[stacktrace]
#0 {main}
"} 
[2025-07-05 13:17:22] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-05 13:17:39] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-05 13:17:53] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-08 20:22:50] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-08 20:23:09] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-08 20:23:24] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-08 22:32:24] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'stocks' already exists (Connection: mysql, SQL: create table `stocks` (`id` bigint unsigned not null auto_increment primary key, `reference` varchar(255) not null, `name` varchar(255) null, `product_id` bigint unsigned not null, `movement` enum('entry', 'output') not null default 'entry', `quantity` int not null default '0', `status` varchar(255) not null default 'active', `date_stock` date null, `description` text null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'stocks' already exists (Connection: mysql, SQL: create table `stocks` (`id` bigint unsigned not null auto_increment primary key, `reference` varchar(255) not null, `name` varchar(255) null, `product_id` bigint unsigned not null, `movement` enum('entry', 'output') not null default 'entry', `quantity` int not null default '0', `status` varchar(255) not null default 'active', `date_stock` date null, `description` text null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\database\\migrations\\2024_11_23_124816_create_stocks_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#32 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'stocks' already exists at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\database\\migrations\\2024_11_23_124816_create_stocks_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#32 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#33 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#34 {main}
"} 
[2025-07-09 05:34:03] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-09 05:34:11] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-09 05:34:34] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-09 05:44:42] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'subscription_revenue' cannot be null (Connection: mysql, SQL: insert into `pack_performances` (`id`, `pack_name`, `carnet_price`, `total_subscriptions`, `successful_subscriptions`, `subscription_revenue`, `total_cotisations`, `completed_cotisations`, `cotisation_revenue`, `performance_score`, `updated_at`, `created_at`) values (35, 01 TRICYCLE  APSONIC AP 200 Q7, 200, 0, 0, ?, 0, 0, ?, ?, 2025-07-09 05:44:42, 2025-07-09 05:44:42)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'subscription_revenue' cannot be null (Connection: mysql, SQL: insert into `pack_performances` (`id`, `pack_name`, `carnet_price`, `total_subscriptions`, `successful_subscriptions`, `subscription_revenue`, `total_cotisations`, `completed_cotisations`, `cotisation_revenue`, `performance_score`, `updated_at`, `created_at`) values (35, 01 TRICYCLE  APSONIC AP 200 Q7, 200, 0, 0, ?, 0, 0, ?, ?, 2025-07-09 05:44:42, 2025-07-09 05:44:42)) at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\MySqlConnection->insert()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insert()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1310): Illuminate\\Database\\Eloquent\\Builder->__call()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(307): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1024): tap()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Console\\Commands\\RefreshPackPerformance.php(107): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\RefreshPackPerformance->handle()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#26 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'subscription_revenue' cannot be null at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:45)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(45): PDOStatement->execute()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\MySqlConnection->insert()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insert()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1310): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(307): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1024): tap()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Console\\Commands\\RefreshPackPerformance.php(107): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\RefreshPackPerformance->handle()
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 {main}
"} 
[2025-07-09 05:56:40] local.ERROR: SQLSTATE[22003]: Numeric value out of range: 1264 Out of range value for column 'performance_score' at row 1 (Connection: mysql, SQL: insert into `pack_performances` (`id`, `pack_name`, `carnet_price`, `total_subscriptions`, `successful_subscriptions`, `subscription_revenue`, `total_cotisations`, `completed_cotisations`, `cotisation_revenue`, `performance_score`, `updated_at`, `created_at`) values (63, 01 BOUTEILLE DE GAZ SODIGAZ 6KG AVEC TETE+12 PLATS INCASSABLES, 200, 1862, 638, 372400, 1785, 638, 32525900, 1129.85, 2025-07-09 05:56:40, 2025-07-09 05:56:40)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 22003): SQLSTATE[22003]: Numeric value out of range: 1264 Out of range value for column 'performance_score' at row 1 (Connection: mysql, SQL: insert into `pack_performances` (`id`, `pack_name`, `carnet_price`, `total_subscriptions`, `successful_subscriptions`, `subscription_revenue`, `total_cotisations`, `completed_cotisations`, `cotisation_revenue`, `performance_score`, `updated_at`, `created_at`) values (63, 01 BOUTEILLE DE GAZ SODIGAZ 6KG AVEC TETE+12 PLATS INCASSABLES, 200, 1862, 638, 372400, 1785, 638, 32525900, 1129.85, 2025-07-09 05:56:40, 2025-07-09 05:56:40)) at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\MySqlConnection->insert()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insert()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1310): Illuminate\\Database\\Eloquent\\Builder->__call()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(307): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1024): tap()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Console\\Commands\\RefreshPackPerformance.php(107): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\RefreshPackPerformance->handle()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#26 {main}

[previous exception] [object] (PDOException(code: 22003): SQLSTATE[22003]: Numeric value out of range: 1264 Out of range value for column 'performance_score' at row 1 at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:45)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(45): PDOStatement->execute()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\MySqlConnection->insert()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insert()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1310): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(307): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1024): tap()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Console\\Commands\\RefreshPackPerformance.php(107): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\RefreshPackPerformance->handle()
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 {main}
"} 
[2025-07-09 06:59:05] local.ERROR: syntax error, unexpected token "}", expecting ";" or "{" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"}\", expecting \";\" or \"{\" at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Console\\Commands\\RefreshClientPerformance.php:17)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}()
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(346): is_subclass_of()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\app\\Console\\Kernel.php(33): Illuminate\\Foundation\\Console\\Kernel->load()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(453): App\\Console\\Kernel->commands()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#6 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle()
#7 {main}
"} 
[2025-07-11 01:10:58] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-11 01:11:13] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-11 01:11:20] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-11 03:39:17] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-11 03:39:28] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-11 03:39:34] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-11 07:27:01] local.ERROR: SQLSTATE[HY000]: General error: 3780 Referencing column 'parent_id' and referenced column 'id' in foreign key constraint 'agencies_parent_id_foreign' are incompatible. (Connection: mysql, SQL: alter table `agencies` add constraint `agencies_parent_id_foreign` foreign key (`parent_id`) references `agencies` (`id`) on delete set null) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 3780 Referencing column 'parent_id' and referenced column 'id' in foreign key constraint 'agencies_parent_id_foreign' are incompatible. (Connection: mysql, SQL: alter table `agencies` add constraint `agencies_parent_id_foreign` foreign key (`parent_id`) references `agencies` (`id`) on delete set null) at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(444): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->table()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\database\\migrations\\2025_07_11_012538_create_foreign_keys_table.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#32 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 3780 Referencing column 'parent_id' and referenced column 'id' in foreign key constraint 'agencies_parent_id_foreign' are incompatible. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(444): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->table()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\database\\migrations\\2025_07_11_012538_create_foreign_keys_table.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#32 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#33 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#34 {main}
"} 
[2025-07-11 07:30:20] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-11 07:30:30] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-11 07:30:36] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-11 07:37:57] local.ERROR: SQLSTATE[HY000]: General error: 3780 Referencing column 'city_id' and referenced column 'id' in foreign key constraint 'agencies_city_id_foreign' are incompatible. (Connection: mysql, SQL: alter table `agencies` add constraint `agencies_city_id_foreign` foreign key (`city_id`) references `cities` (`id`) on delete cascade) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 3780 Referencing column 'city_id' and referenced column 'id' in foreign key constraint 'agencies_city_id_foreign' are incompatible. (Connection: mysql, SQL: alter table `agencies` add constraint `agencies_city_id_foreign` foreign key (`city_id`) references `cities` (`id`) on delete cascade) at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(444): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->table()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\database\\migrations\\2025_07_11_012538_create_foreign_keys_table.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#32 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 3780 Referencing column 'city_id' and referenced column 'id' in foreign key constraint 'agencies_city_id_foreign' are incompatible. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(444): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->table()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\database\\migrations\\2025_07_11_012538_create_foreign_keys_table.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#32 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#33 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#34 {main}
"} 
[2025-07-12 04:55:27] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-12 04:55:38] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-12 04:55:46] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-12 04:57:59] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-12 04:58:03] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-12 04:58:14] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-12 05:12:21] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP 'wallets_user_id_foreign'; check that column/key exists (Connection: mysql, SQL: alter table `wallets` drop foreign key `wallets_user_id_foreign`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP 'wallets_user_id_foreign'; check that column/key exists (Connection: mysql, SQL: alter table `wallets` drop foreign key `wallets_user_id_foreign`) at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(444): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->table()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\database\\migrations\\2025_07_11_012538_create_foreign_keys_table.php(19): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#32 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP 'wallets_user_id_foreign'; check that column/key exists at C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run()
#4 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement()
#5 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(444): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->table()
#8 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\database\\migrations\\2025_07_11_012538_create_foreign_keys_table.php(19): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#24 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#26 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#27 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute()
#28 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#29 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run()
#30 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#31 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#32 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#33 C:\\WorkSpace\\FRIKALAB-GROUP\\IZICOLLECT\\main-service\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#34 {main}
"} 
