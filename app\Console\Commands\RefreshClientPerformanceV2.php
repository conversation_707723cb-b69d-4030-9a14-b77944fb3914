<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ClientPerformance;
use Illuminate\Support\Facades\DB;

class RefreshClientPerformanceV2 extends Command
{
    protected $signature   = 'refresh:client {--force : Force la mise à jour complète}';
    protected $description = 'Version optimisée avec sous-requêtes pour recalculer les statistiques des clients';

    public function handle()
    {
        $this->info("🔍 Version optimisée - Recherche des clients impactés...");

        // Augmenter temporairement les timeouts MySQL
        $this->setMySQLTimeouts();

        try {
            $forceUpdate = $this->option('force');
            $isEmpty = ClientPerformance::count() === 0;

            if ($isEmpty || $forceUpdate) {
                $this->info("🆕 Mise à jour complète avec requête optimisée...");
                $this->processWithOptimizedQuery();
            } else {
                $this->processRecentlyUpdatedClients();
            }
        } finally {
            $this->restoreMySQLTimeouts();
        }
    }

    /**
     * Traite avec une requête optimisée utilisant des sous-requêtes
     */
    private function processWithOptimizedQuery(?array $clientIds = null)
    {
        if ($clientIds === null) {
            ClientPerformance::truncate();
        } else {
            ClientPerformance::whereIn('id', $clientIds)->delete();
        }

        $whereClause = $clientIds ? "WHERE c.id IN (" . implode(',', $clientIds) . ")" : "";

        $query = "
            INSERT INTO client_performances (
                id, nom, prenoms, phone, email,
                finished_subscriptions_count, subscription_revenue,
                finished_cotisations_count, cotisation_revenue,
                total_revenue, engagement_score, last_updated,
                created_at, updated_at
            )
            SELECT
                c.id,
                c.nom,
                c.prenoms,
                c.phone,
                c.email,
                COALESCE(s.finished_count, 0) as finished_subscriptions_count,
                COALESCE(s.revenue, 0) as subscription_revenue,
                COALESCE(co.finished_count, 0) as finished_cotisations_count,
                COALESCE(co.revenue, 0) as cotisation_revenue,
                COALESCE(s.revenue, 0) + COALESCE(co.revenue, 0) as total_revenue,
                ROUND(
                    (COALESCE(s.finished_count, 0) * 0.4) +
                    (COALESCE(s.revenue, 0) / 10000 * 0.4) +
                    (COALESCE(co.revenue, 0) / 10000 * 0.2)
                , 2) as raw_engagement_score,
                NOW() as last_updated,
                NOW() as created_at,
                NOW() as updated_at
            FROM clients c
            LEFT JOIN (
                SELECT
                    client_id,
                    COUNT(CASE WHEN status IN ('finished', 'delivered') THEN 1 END) as finished_count,
                    COALESCE(SUM(CASE WHEN status IN ('finished', 'delivered') THEN carnet_price ELSE 0 END), 0) as revenue
                FROM subscriptions
                GROUP BY client_id
            ) s ON c.id = s.client_id
            LEFT JOIN (
                SELECT
                    client_id,
                    COUNT(CASE WHEN status = 'finished' THEN 1 END) as finished_count,
                    COALESCE(SUM(CASE WHEN status = 'finished' THEN total_amount ELSE 0 END), 0) as revenue
                FROM cotisations
                GROUP BY client_id
            ) co ON c.id = co.client_id
            {$whereClause}
        ";

        DB::statement($query);

        // Normalisation des scores
        $this->normalizeEngagementScores();

        $count = ClientPerformance::count();
        $this->info("✅ {$count} clients traités avec la requête optimisée !");
    }

    /**
     * Normalise les scores d'engagement (0-100)
     */
    private function normalizeEngagementScores()
    {
        $maxScore = DB::table('client_performances')->max('engagement_score');

        if ($maxScore > 0) {
            DB::statement("
                UPDATE client_performances
                SET engagement_score = LEAST(100.00, ROUND((engagement_score / {$maxScore}) * 100, 2))
            ");
        }
    }

    /**
     * Traite uniquement les clients récemment modifiés
     */
    private function processRecentlyUpdatedClients()
    {
        $recentClientIds = DB::select("
            SELECT DISTINCT c.id
            FROM clients c
            WHERE c.updated_at > NOW() - INTERVAL 24 HOUR

            UNION

            SELECT DISTINCT s.client_id as id
            FROM subscriptions s
            WHERE s.updated_at > NOW() - INTERVAL 24 HOUR

            UNION

            SELECT DISTINCT co.client_id as id
            FROM cotisations co
            WHERE co.updated_at > NOW() - INTERVAL 24 HOUR
        ");

        if (empty($recentClientIds)) {
            $this->info("✅ Aucun client à mettre à jour.");
            return;
        }

        $idsToUpdate = array_map(fn($row) => $row->id, $recentClientIds);
        $this->info("♻️ " . count($idsToUpdate) . " clients trouvés. Mise à jour en cours...");

        // Traitement par batch pour éviter les requêtes trop longues
        $batches = array_chunk($idsToUpdate, 1000);

        foreach ($batches as $index => $batch) {
            $this->info("📊 Traitement du batch " . ($index + 1) . "/" . count($batches) . "...");
            $this->processWithOptimizedQuery($batch);
        }

        $this->info("✅ " . count($idsToUpdate) . " clients mis à jour !");
    }

    /**
     * Configure les timeouts MySQL pour les requêtes longues
     */
    private function setMySQLTimeouts()
    {
        try {
            // Configuration des timeouts MySQL appropriés
            DB::statement('SET SESSION wait_timeout = 300'); // 5 minutes
            DB::statement('SET SESSION interactive_timeout = 300'); // 5 minutes
            DB::statement('SET SESSION net_read_timeout = 300'); // Timeout de lecture réseau
            DB::statement('SET SESSION net_write_timeout = 300'); // Timeout d'écriture réseau
            $this->info("⚙️ Timeouts MySQL configurés pour les requêtes longues");
        } catch (\Exception $e) {
            $this->warn("⚠️ Impossible de configurer les timeouts MySQL : " . $e->getMessage());
        }
    }

    /**
     * Restaure les timeouts MySQL par défaut
     */
    private function restoreMySQLTimeouts()
    {
        try {
            DB::statement('SET SESSION wait_timeout = DEFAULT');
            DB::statement('SET SESSION interactive_timeout = DEFAULT');
            DB::statement('SET SESSION net_read_timeout = DEFAULT');
            DB::statement('SET SESSION net_write_timeout = DEFAULT');
            $this->info("⚙️ Timeouts MySQL restaurés");
        } catch (\Exception $e) {
            $this->warn("⚠️ Impossible de restaurer les timeouts MySQL : " . $e->getMessage());
        }
    }
}
