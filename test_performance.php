<?php

/**
 * Script de test des performances pour RefreshClientPerformance
 * Usage: php test_performance.php
 */

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use App\Models\ClientPerformance;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🧪 Test des performances RefreshClientPerformance\n";
echo "================================================\n\n";

// Test 1: Vérifier les index
echo "1️⃣ Vérification des index...\n";

$tables = ['subscriptions', 'cotisations', 'clients'];
foreach ($tables as $table) {
    try {
        $indexes = DB::select("SHOW INDEX FROM {$table}");
        $indexNames = array_map(fn($idx) => $idx->Key_name, $indexes);

        echo "📋 Table {$table}:\n";
        foreach ($indexNames as $indexName) {
            if (strpos($indexName, 'idx_') === 0) {
                echo "   ✅ {$indexName}\n";
            }
        }
        echo "\n";
    } catch (Exception $e) {
        echo "   ❌ Erreur: {$e->getMessage()}\n\n";
    }
}

// Test 2: Compter les enregistrements
echo "2️⃣ Statistiques des tables...\n";

$stats = [
    'clients' => DB::table('clients')->count(),
    'subscriptions' => DB::table('subscriptions')->count(),
    'cotisations' => DB::table('cotisations')->count(),
    'client_performances' => DB::table('client_performances')->count(),
];

foreach ($stats as $table => $count) {
    echo "📊 {$table}: " . number_format($count) . " enregistrements\n";
}
echo "\n";

// Test 3: Test de performance d'une requête simple
echo "3️⃣ Test de performance des requêtes...\n";

$start = microtime(true);

// Test requête avec index
$result = DB::table('subscriptions')
    ->where('client_id', 1)
    ->where('status', 'finished')
    ->count();

$duration = (microtime(true) - $start) * 1000;
echo "⚡ Requête indexée (client_id + status): " . number_format($duration, 2) . "ms\n";

$start = microtime(true);

// Test requête de détection des changements récents
$recentCount = DB::table('subscriptions')
    ->where('updated_at', '>', DB::raw('NOW() - INTERVAL 24 HOUR'))
    ->count();

$duration = (microtime(true) - $start) * 1000;
echo "⚡ Requête changements récents: " . number_format($duration, 2) . "ms ({$recentCount} résultats)\n\n";

// Test 4: Simulation d'un batch
echo "4️⃣ Test de performance d'un batch...\n";

$start = microtime(true);

$clientIds = DB::table('clients')->limit(100)->pluck('id')->toArray();

if (!empty($clientIds)) {
    // Test des requêtes optimisées
    $subscriptionData = DB::table('subscriptions')
        ->select(
            'client_id',
            DB::raw('COUNT(CASE WHEN status IN ("finished", "delivered") THEN 1 END) as finished_count'),
            DB::raw('COALESCE(SUM(CASE WHEN status IN ("finished", "delivered") THEN carnet_price ELSE 0 END), 0) as revenue')
        )
        ->whereIn('client_id', $clientIds)
        ->groupBy('client_id')
        ->get();

    $cotisationData = DB::table('cotisations')
        ->select(
            'client_id',
            DB::raw('COUNT(CASE WHEN status = "finished" THEN 1 END) as finished_count'),
            DB::raw('COALESCE(SUM(CASE WHEN status = "finished" THEN total_amount ELSE 0 END), 0) as revenue')
        )
        ->whereIn('client_id', $clientIds)
        ->groupBy('client_id')
        ->get();

    $duration = (microtime(true) - $start) * 1000;
    echo "⚡ Batch de 100 clients: " . number_format($duration, 2) . "ms\n";
    echo "📈 Subscriptions trouvées: " . $subscriptionData->count() . "\n";
    echo "📈 Cotisations trouvées: " . $cotisationData->count() . "\n\n";
} else {
    echo "⚠️ Aucun client trouvé pour le test\n\n";
}

// Test 5: Vérifier l'état de client_performances
echo "5️⃣ État de la table client_performances...\n";

$performanceStats = [
    'total' => ClientPerformance::count(),
    'last_updated' => ClientPerformance::max('last_updated'),
    'avg_score' => ClientPerformance::avg('engagement_score'),
    'max_revenue' => ClientPerformance::max('total_revenue'),
];

foreach ($performanceStats as $key => $value) {
    if ($value !== null) {
        if (is_numeric($value)) {
            $value = number_format($value, 2);
        }
        echo "📊 {$key}: {$value}\n";
    } else {
        echo "📊 {$key}: N/A\n";
    }
}

echo "\n";

// Test 6: Recommandations
echo "6️⃣ Recommandations...\n";

if ($stats['client_performances'] === 0) {
    echo "⚠️ Table client_performances vide. Exécuter: php artisan refresh:client\n";
} elseif ($performanceStats['last_updated'] &&
        strtotime($performanceStats['last_updated']) < strtotime('-1 day')) {
    echo "⚠️ Données anciennes (> 24h). Considérer une mise à jour.\n";
} else {
    echo "✅ Données à jour et cohérentes.\n";
}

if ($stats['clients'] > 10000) {
    echo "💡 Volume important ({$stats['clients']} clients). Utiliser refresh:client-v2 pour de meilleures performances.\n";
}

if ($stats['subscriptions'] > 50000 || $stats['cotisations'] > 50000) {
    echo "💡 Gros volume de données. Considérer l'exécution en heures creuses.\n";
}

echo "\n🎉 Test terminé !\n";
