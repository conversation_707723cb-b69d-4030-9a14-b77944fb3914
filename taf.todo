public function up(): void
{
    // Désactiver la vérification des clés étrangères
    DB::statement('SET FOREIGN_KEY_CHECKS=0;');

    try {
        // Première étape : Corriger les types de colonnes pour la compatibilité des clés étrangères
        $this->fixColumnTypes();

        // agencies table
        Schema::table('agencies', function (Blueprint $table) {
            $table->foreign('parent_id')->references('id')->on('agencies')->onDelete('set null');
            $table->foreign('city_id')->references('id')->on('cities')->onDelete('cascade');
            $table->foreign('quarter_id')->references('id')->on('quarters')->onDelete('set null');
            $table->foreign('responsable_id')->references('id')->on('personals')->onDelete('set null');
            $table->index('city_id');
            $table->index('quarter_id');
            $table->index('responsable_id');
            $table->index('status');
            $table->index('name');
            // Index composites pour les requêtes d'agences
            $table->index(['city_id', 'status'], 'idx_agency_city_status');
            $table->index(['parent_id', 'status'], 'idx_agency_parent_status');
            $table->index(['responsable_id', 'status'], 'idx_agency_resp_status');
        });

        // agency_personals table
        Schema::table('agency_personals', function (Blueprint $table) {
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('cascade');
            $table->foreign('personal_id')->references('id')->on('personals')->onDelete('cascade');
            $table->foreign('role_id')->references('id')->on('roles')->onDelete('cascade');
            $table->index('agency_id');
            $table->index('personal_id');
            $table->index('role_id');
            $table->index('is_current');
        });

        // app_configurations table
        Schema::table('app_configurations', function (Blueprint $table) {
            $table->index('app_type');
        });

        // categories table
        Schema::table('categories', function (Blueprint $table) {
            $table->index('name');
        });

        // cities table
        Schema::table('cities', function (Blueprint $table) {
            $table->foreign('country_id')->references('id')->on('countries')->onDelete('cascade');
            $table->index('country_id');
            $table->index('name');
        });

        // client_collectors table
        Schema::table('client_collectors', function (Blueprint $table) {
            $table->foreign('client_id')->references('id')->on('clients')->onDelete('cascade');
            $table->foreign('collector_id')->references('id')->on('personals')->onDelete('cascade');
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('cascade');
            $table->index('client_id');
            $table->index('collector_id');
            $table->index('agency_id');
            $table->index('is_principal');
        });

        // client_performances table
        Schema::table('client_performances', function (Blueprint $table) {
            $table->index('nom');
            $table->index('phone');
            $table->index('email');
            $table->index('last_updated');
        });

        // clients table
        Schema::table('clients', function (Blueprint $table) {
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('set null');
            $table->foreign('city_id')->references('id')->on('cities')->onDelete('set null');
            $table->foreign('quarter_id')->references('id')->on('quarters')->onDelete('set null');
            $table->foreign('collector_id')->references('id')->on('personals')->onDelete('set null');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->index('agency_id');
            $table->index('city_id');
            $table->index('quarter_id');
            $table->index('collector_id');
            $table->index('user_id');
            $table->index('status');
            $table->index('nom');
        });

        // controls table
        Schema::table('controls', function (Blueprint $table) {
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('cascade');
            $table->foreign('supervisor_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('collector_id')->references('id')->on('personals')->onDelete('cascade');
            $table->foreign('client_id')->references('id')->on('clients')->onDelete('cascade');
            $table->foreign('subscription_id')->references('id')->on('subscriptions')->onDelete('cascade');
            $table->index('agency_id');
            $table->index('supervisor_id');
            $table->index('collector_id');
            $table->index('client_id');
            $table->index('subscription_id');
            $table->index('status');
            $table->index('date_control');
            // Index composites pour les requêtes de contrôle
            $table->index(['agency_id', 'date_control'], 'idx_ctrl_agency_date');
            $table->index(['collector_id', 'status'], 'idx_ctrl_coll_status');
            $table->index(['supervisor_id', 'date_control'], 'idx_ctrl_super_date');
            $table->index(['client_id', 'status'], 'idx_ctrl_client_status');
        });

        // cotisations table
        Schema::table('cotisations', function (Blueprint $table) {
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('cascade');
            $table->foreign('client_id')->references('id')->on('clients')->onDelete('cascade');
            $table->foreign('collector_id')->references('id')->on('personals')->onDelete('cascade');
            $table->foreign('subscription_id')->references('id')->on('subscriptions')->onDelete('cascade');
            $table->index('agency_id');
            $table->index('client_id');
            $table->index('collector_id');
            $table->index('subscription_id');
            $table->index('status');
            $table->index('date_cotise');
        });

        // countries table
        Schema::table('countries', function (Blueprint $table) {
            $table->index('name');
            $table->index('code');
        });

        // deliveries table (already has foreign keys for client_id and pack_id)
        Schema::table('deliveries', function (Blueprint $table) {
            $table->foreign('delivery_men')->references('id')->on('personals')->onDelete('set null');
            $table->index('delivery_men');
            $table->index('delivery_date');
        });

        // failed_jobs table
        Schema::table('failed_jobs', function (Blueprint $table) {
            $table->index('failed_at');
        });

        // pack_performances table
        Schema::table('pack_performances', function (Blueprint $table) {
            $table->index('pack_name');
            $table->index('last_updated');
        });

        // pack_products table
        Schema::table('pack_products', function (Blueprint $table) {
            $table->foreign('pack_id')->references('id')->on('packs')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
            $table->index('pack_id');
            $table->index('product_id');
            $table->index('status');
        });

        // packs table
        Schema::table('packs', function (Blueprint $table) {
            $table->index('name');
            $table->index('status');
            $table->index('category');
        });

        // payments table
        Schema::table('payments', function (Blueprint $table) {
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('cascade');
            $table->foreign('collector_id')->references('id')->on('personals')->onDelete('cascade');
            $table->foreign('subscription_id')->references('id')->on('subscriptions')->onDelete('set null');
            $table->foreign('cotisation_id')->references('id')->on('cotisations')->onDelete('set null');
            $table->foreign('pack_id')->references('id')->on('packs')->onDelete('set null');
            $table->foreign('client_id')->references('id')->on('clients')->onDelete('set null');
            $table->foreign('versement_id')->references('id')->on('versements')->onDelete('set null');
            $table->index('agency_id');
            $table->index('collector_id');
            $table->index('subscription_id');
            $table->index('cotisation_id');
            $table->index('pack_id');
            $table->index('client_id');
            $table->index('versement_id');
            $table->index('status');
            $table->index('payment_date');
            $table->index('payment_mode');
            // Index composites critiques pour les requêtes de reporting
            $table->index(['collector_id', 'payment_date'], 'idx_pay_coll_date');
            $table->index(['agency_id', 'payment_date'], 'idx_pay_agency_date');
            $table->index(['cotisation_id', 'payment_date'], 'idx_pay_cotis_date');
            $table->index(['subscription_id', 'cotisation_id'], 'idx_pay_sub_cotis');
            $table->index(['payment_type', 'status'], 'idx_pay_type_status');
            $table->index(['collector_id', 'status'], 'idx_pay_coll_status');
            $table->index(['agency_id', 'status'], 'idx_pay_agency_status');
        });

        // personal_access_tokens table
        Schema::table('personal_access_tokens', function (Blueprint $table) {
            $table->index('tokenable_type');
            $table->index('tokenable_id');
            $table->index('created_at');
        });

        // personals table
        Schema::table('personals', function (Blueprint $table) {
            $table->foreign('city_id')->references('id')->on('cities')->onDelete('set null');
            $table->foreign('quarter_id')->references('id')->on('quarters')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('role_id')->references('id')->on('roles')->onDelete('set null');
            $table->index('city_id');
            $table->index('quarter_id');
            $table->index('user_id');
            $table->index('role_id');
            $table->index('status');
            $table->index('nom');
            $table->index('email');
            $table->index('phone');
        });

        // products table
        Schema::table('products', function (Blueprint $table) {
            $table->foreign('category_id')->references('id')->on('categories')->onDelete('set null');
            $table->index('category_id');
            $table->index('name');
            $table->index('status');
            $table->index('code');
        });

        // quarters table
        Schema::table('quarters', function (Blueprint $table) {
            $table->foreign('city_id')->references('id')->on('cities')->onDelete('cascade');
            $table->index('city_id');
            $table->index('name');
        });

        // roles table
        Schema::table('roles', function (Blueprint $table) {
            $table->index('name');
        });

        // stocks table (already has product_id foreign key)
        Schema::table('stocks', function (Blueprint $table) {
            $table->index('reference');
            $table->index('status');
            $table->index('date_stock');
        });

        // subscriptions table
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('set null');
            $table->foreign('pack_id')->references('id')->on('packs')->onDelete('cascade');
            $table->foreign('client_id')->references('id')->on('clients')->onDelete('cascade');
            $table->foreign('collector_id')->references('id')->on('personals')->onDelete('set null');
            $table->index('agency_id');
            $table->index('pack_id');
            $table->index('client_id');
            $table->index('collector_id');
            $table->index('status');
            $table->index('code');
        });

        // transactions table
        Schema::table('transactions', function (Blueprint $table) {
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('set null');
            $table->foreign('wallet_id')->references('id')->on('wallets')->onDelete('cascade');
            $table->index('user_id');
            $table->index('agency_id');
            $table->index('wallet_id');
            $table->index('status');
            $table->index('date_transact');
            // Index composites pour les requêtes de transactions
            $table->index(['user_id', 'status'], 'idx_trans_user_status');
            $table->index(['agency_id', 'date_transact'], 'idx_trans_agency_date');
            $table->index(['wallet_id', 'status'], 'idx_trans_wallet_status');
            $table->index(['status', 'date_transact'], 'idx_trans_status_date');
        });

        // users table
        Schema::table('users', function (Blueprint $table) {
            $table->foreign('role_id')->references('id')->on('roles')->onDelete('cascade');
            $table->index('role_id');
            $table->index('username');
            $table->index('status');
        });

        // versements table
        Schema::table('versements', function (Blueprint $table) {
            $table->foreign('collector_id')->references('id')->on('personals')->onDelete('set null');
            $table->foreign('cashier_id')->references('id')->on('personals')->onDelete('set null');
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('set null');
            $table->index('collector_id');
            $table->index('cashier_id');
            $table->index('agency_id');
            $table->index('status');
            $table->index('payment_date');
            $table->index('trx_ref');
            // Index composites pour les requêtes de versements
            $table->index(['collector_id', 'status'], 'idx_vers_coll_status');
            $table->index(['cashier_id', 'status'], 'idx_vers_cash_status');
            $table->index(['agency_id', 'status'], 'idx_vers_agency_status');
            $table->index(['agency_id', 'payment_date'], 'idx_vers_agency_date');
            $table->index(['collector_id', 'payment_date'], 'idx_vers_coll_date');
            $table->index(['status', 'created_at'], 'idx_vers_status_created');
        });

        // wallets table
        Schema::table('wallets', function (Blueprint $table) {
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('set null');
            $table->index('user_id');
            $table->index('agency_id');
            $table->index('status');
            $table->index('code');
        });
    } finally {
        // Réactiver la vérification des clés étrangères
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }
}


/**
    * Corriger les types de colonnes pour la compatibilité des clés étrangères
    */
private function fixColumnTypes(): void
{
    // Corriger les colonnes de type integer vers unsignedBigInteger pour correspondre aux id()

    // Table agencies
    Schema::table('agencies', function (Blueprint $table) {
        $table->unsignedBigInteger('parent_id')->nullable()->change();
        $table->unsignedBigInteger('city_id')->change();
        $table->unsignedBigInteger('quarter_id')->nullable()->change();
        $table->unsignedBigInteger('responsable_id')->nullable()->change();
    });

    // Table agency_personals
    Schema::table('agency_personals', function (Blueprint $table) {
        $table->unsignedBigInteger('agency_id')->change();
        $table->unsignedBigInteger('personal_id')->change();
        $table->unsignedBigInteger('role_id')->change();
    });

    // Table cities
    Schema::table('cities', function (Blueprint $table) {
        $table->unsignedBigInteger('country_id')->change();
    });

    // Table quarters
    Schema::table('quarters', function (Blueprint $table) {
        $table->unsignedBigInteger('city_id')->change();
    });

    // Table personals
    Schema::table('personals', function (Blueprint $table) {
        $table->unsignedBigInteger('city_id')->nullable()->change();
        $table->unsignedBigInteger('quarter_id')->change();
        $table->unsignedBigInteger('user_id')->nullable()->change();
        $table->unsignedBigInteger('role_id')->nullable()->change();
    });

    // Table clients (si elle existe)
    if (Schema::hasTable('clients')) {
        Schema::table('clients', function (Blueprint $table) {
            if (Schema::hasColumn('clients', 'agency_id')) {
                $table->unsignedBigInteger('agency_id')->nullable()->change();
            }
            if (Schema::hasColumn('clients', 'city_id')) {
                $table->unsignedBigInteger('city_id')->nullable()->change();
            }
            if (Schema::hasColumn('clients', 'quarter_id')) {
                $table->unsignedBigInteger('quarter_id')->nullable()->change();
            }
            if (Schema::hasColumn('clients', 'collector_id')) {
                $table->unsignedBigInteger('collector_id')->nullable()->change();
            }
            if (Schema::hasColumn('clients', 'user_id')) {
                $table->unsignedBigInteger('user_id')->nullable()->change();
            }
        });
    }

    // Table client_collectors (si elle existe)
    if (Schema::hasTable('client_collectors')) {
        Schema::table('client_collectors', function (Blueprint $table) {
            if (Schema::hasColumn('client_collectors', 'client_id')) {
                $table->unsignedBigInteger('client_id')->change();
            }
            if (Schema::hasColumn('client_collectors', 'collector_id')) {
                $table->unsignedBigInteger('collector_id')->change();
            }
            if (Schema::hasColumn('client_collectors', 'agency_id')) {
                $table->unsignedBigInteger('agency_id')->change();
            }
        });
    }

    // Table products (si elle existe)
    if (Schema::hasTable('products')) {
        Schema::table('products', function (Blueprint $table) {
            if (Schema::hasColumn('products', 'category_id')) {
                $table->unsignedBigInteger('category_id')->nullable()->change();
            }
        });
    }

    // Table pack_products (si elle existe)
    if (Schema::hasTable('pack_products')) {
        Schema::table('pack_products', function (Blueprint $table) {
            if (Schema::hasColumn('pack_products', 'pack_id')) {
                $table->unsignedBigInteger('pack_id')->change();
            }
            if (Schema::hasColumn('pack_products', 'product_id')) {
                $table->unsignedBigInteger('product_id')->change();
            }
        });
    }

    // Table subscriptions (si elle existe)
    if (Schema::hasTable('subscriptions')) {
        Schema::table('subscriptions', function (Blueprint $table) {
            if (Schema::hasColumn('subscriptions', 'agency_id')) {
                $table->unsignedBigInteger('agency_id')->nullable()->change();
            }
            if (Schema::hasColumn('subscriptions', 'pack_id')) {
                $table->unsignedBigInteger('pack_id')->change();
            }
            if (Schema::hasColumn('subscriptions', 'client_id')) {
                $table->unsignedBigInteger('client_id')->change();
            }
            if (Schema::hasColumn('subscriptions', 'collector_id')) {
                $table->unsignedBigInteger('collector_id')->nullable()->change();
            }
        });
    }

    // Table cotisations (si elle existe)
    if (Schema::hasTable('cotisations')) {
        Schema::table('cotisations', function (Blueprint $table) {
            if (Schema::hasColumn('cotisations', 'agency_id')) {
                $table->unsignedBigInteger('agency_id')->change();
            }
            if (Schema::hasColumn('cotisations', 'client_id')) {
                $table->unsignedBigInteger('client_id')->change();
            }
            if (Schema::hasColumn('cotisations', 'collector_id')) {
                $table->unsignedBigInteger('collector_id')->change();
            }
            if (Schema::hasColumn('cotisations', 'subscription_id')) {
                $table->unsignedBigInteger('subscription_id')->change();
            }
        });
    }

    // Table controls (si elle existe)
    if (Schema::hasTable('controls')) {
        Schema::table('controls', function (Blueprint $table) {
            if (Schema::hasColumn('controls', 'agency_id')) {
                $table->unsignedBigInteger('agency_id')->change();
            }
            if (Schema::hasColumn('controls', 'supervisor_id')) {
                $table->unsignedBigInteger('supervisor_id')->change();
            }
            if (Schema::hasColumn('controls', 'collector_id')) {
                $table->unsignedBigInteger('collector_id')->change();
            }
            if (Schema::hasColumn('controls', 'client_id')) {
                $table->unsignedBigInteger('client_id')->change();
            }
            if (Schema::hasColumn('controls', 'subscription_id')) {
                $table->unsignedBigInteger('subscription_id')->change();
            }
        });
    }

    // Table payments (si elle existe)
    if (Schema::hasTable('payments')) {
        Schema::table('payments', function (Blueprint $table) {
            if (Schema::hasColumn('payments', 'agency_id')) {
                $table->unsignedBigInteger('agency_id')->change();
            }
            if (Schema::hasColumn('payments', 'collector_id')) {
                $table->unsignedBigInteger('collector_id')->change();
            }
            if (Schema::hasColumn('payments', 'subscription_id')) {
                $table->unsignedBigInteger('subscription_id')->nullable()->change();
            }
            if (Schema::hasColumn('payments', 'cotisation_id')) {
                $table->unsignedBigInteger('cotisation_id')->nullable()->change();
            }
            if (Schema::hasColumn('payments', 'pack_id')) {
                $table->unsignedBigInteger('pack_id')->nullable()->change();
            }
            if (Schema::hasColumn('payments', 'client_id')) {
                $table->unsignedBigInteger('client_id')->nullable()->change();
            }
            if (Schema::hasColumn('payments', 'versement_id')) {
                $table->unsignedBigInteger('versement_id')->nullable()->change();
            }
        });
    }

    // Table users (si elle existe)
    if (Schema::hasTable('users')) {
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'role_id')) {
                $table->unsignedBigInteger('role_id')->change();
            }
        });
    }

    // Table versements (si elle existe)
    if (Schema::hasTable('versements')) {
        Schema::table('versements', function (Blueprint $table) {
            if (Schema::hasColumn('versements', 'collector_id')) {
                $table->unsignedBigInteger('collector_id')->nullable()->change();
            }
            if (Schema::hasColumn('versements', 'cashier_id')) {
                $table->unsignedBigInteger('cashier_id')->nullable()->change();
            }
            if (Schema::hasColumn('versements', 'agency_id')) {
                $table->unsignedBigInteger('agency_id')->nullable()->change();
            }
        });
    }

    // Table wallets (si elle existe)
    if (Schema::hasTable('wallets')) {
        Schema::table('wallets', function (Blueprint $table) {
            if (Schema::hasColumn('wallets', 'user_id')) {
                $table->unsignedBigInteger('user_id')->nullable()->change();
            }
            if (Schema::hasColumn('wallets', 'agency_id')) {
                $table->unsignedBigInteger('agency_id')->nullable()->change();
            }
        });
    }

    // Table transactions (si elle existe)
    if (Schema::hasTable('transactions')) {
        Schema::table('transactions', function (Blueprint $table) {
            if (Schema::hasColumn('transactions', 'user_id')) {
                $table->unsignedBigInteger('user_id')->nullable()->change();
            }
            if (Schema::hasColumn('transactions', 'agency_id')) {
                $table->unsignedBigInteger('agency_id')->nullable()->change();
            }
            if (Schema::hasColumn('transactions', 'wallet_id')) {
                $table->unsignedBigInteger('wallet_id')->change();
            }
        });
    }

    // Table deliveries (si elle existe)
    if (Schema::hasTable('deliveries')) {
        Schema::table('deliveries', function (Blueprint $table) {
            if (Schema::hasColumn('deliveries', 'delivery_men')) {
                $table->unsignedBigInteger('delivery_men')->nullable()->change();
            }
        });
    }

}




