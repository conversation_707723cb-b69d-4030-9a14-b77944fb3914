<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command('notify:collectors-reminder')
            ->dailyAt('14:00');

        $schedule->command('notify:collectors-reminder')
            ->dailyAt('15:00');

        $schedule->command('notify:collectors-reminder')
            ->dailyAt('16:00');

        $schedule->command('refresh:pack')
            ->dailyAt('00:00');

        $schedule->command('refresh:client')
            ->dailyAt('01:00');

        // $schedule->command('refresh:client-v2')
        //     ->dailyAt('02:00');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
